/**
 * 大纲驱动的深度研究Hook - 集成长程上下文管理
 * 完全集成 useDeepResearchWithContext 的所有功能
 * 支持基于大纲的研究和智能子章节拆分写作
 */

import { useCallback, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { streamText, generateText } from 'ai';
import { useTaskStore } from '@/store/task';
import { useOutlineStore } from '@/store/outline';
import { useHistoryStore } from '@/store/history';
import useDeepResearch from './useDeepResearch';
import useAiProvider from '@/hooks/useAiProvider';
import {
  generateSubChapterPrompt,
  outputGuidelinesPrompt,
} from '@/constants/prompts';
import { writeReportTitlePrompt, getSystemPrompt } from '@/utils/deep-research/prompts';
import { getEffectiveWritingConfig, getConfiguredDelays } from '@/utils/writing-config-helper';
import { fixFormat, removeThinkTags } from '@/utils/formatFixer';
import { ThinkTagStreamProcessor } from '@/utils/text';
import { validateContentWithAI, isChapterValidationEnabled } from '@/utils/aiValidator';
import { validateContentWithDualModel, isDualModelValidationEnabled } from '@/utils/dualModelValidator';
import { LongContextManager } from '@/utils/deep-research/long-context-manager';
import { ChapterContextCache } from '@/utils/deep-research/chapter-context-cache';
import type { ResearchOutline, OutlineChapter } from '@/types/outline';

// 原生JS实现pick函数
function pick<T extends Record<string, any>, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> {
  const result = {} as Pick<T, K>;
  keys.forEach(key => {
    if (key in obj) {
      result[key] = obj[key];
    }
  });
  return result;
}

// 响应语言提示函数
const getResponseLanguagePrompt = () => `**全部使用简体中文返回**`;

export function useOutlineDrivenResearch() {
  const [status, setStatus] = useState<string>('');
  const [isResearching, setIsResearching] = useState(false);
  const [currentChapter, setCurrentChapter] = useState(0);
  const [id, setId] = useState<string | null>(null);
  
  const contextManagerRef = useRef<LongContextManager | null>(null);
  const { t } = useTranslation();
  
  const taskStore = useTaskStore();
  const { save } = useHistoryStore();
  
  // 使用现有的研究功能
  const { 
    askQuestions, 
    writeReportPlan, 
    deepResearch, 
    writeFinalReport,
    writeChapterReport 
  } = useDeepResearch();
  
  // 使用AI提供者钩子
  const { createModelProvider, getModel } = useAiProvider();

  // 初始化上下文管理器
  const initializeContextManager = (reportTitle: string) => {
    if (!contextManagerRef.current) {
      contextManagerRef.current = new LongContextManager();
    }
    contextManagerRef.current.initializeNewReport(reportTitle);
  };

  // 上下文感知的报告构建器
  class ContextAwareReportBuilder {
    private content = '';
    private currentChapterContent = '';
    private updateCallback: (content: string) => void;
    private updateTimer: NodeJS.Timeout | null = null;

    constructor(onUpdate: (content: string) => void) {
      this.updateCallback = onUpdate;
    }

    append(text: string) {
      this.content += text;
      this.scheduleUpdate();
    }

    updateCurrentChapter(chapterContent: string) {
      this.currentChapterContent = chapterContent;
      this.scheduleRealTimeUpdate();
    }

    // 🔥 添加公共方法来重置和更新内容
    replaceContent(newContent: string) {
      this.content = newContent;
      this.scheduleUpdate();
    }

    private scheduleRealTimeUpdate() {
      if (this.updateTimer) {
        clearTimeout(this.updateTimer);
      }
      this.updateTimer = setTimeout(() => {
        const fullContent = this.content + this.currentChapterContent;
        this.updateCallback(fullContent);
      }, 200); // 200ms 防抖
    }

    private scheduleUpdate() {
      if (this.updateTimer) {
        clearTimeout(this.updateTimer);
      }
      this.updateTimer = setTimeout(() => {
        this.updateCallback(this.content);
      }, 100);
    }

    toString() {
      return this.content;
    }

    destroy() {
      if (this.updateTimer) {
        clearTimeout(this.updateTimer);
      }
    }
  }

  // 内存优化器
  class MemoryOptimizer {
    private gcTimer: NodeJS.Timeout | null = null;

    scheduleCleanup() {
      if (this.gcTimer) {
        clearTimeout(this.gcTimer);
      }
      this.gcTimer = setTimeout(() => {
        this.performCleanup();
      }, 30000); // 30秒后清理
    }

    private performCleanup() {
      if (global.gc) {
        global.gc();
      }
    }

    destroy() {
      if (this.gcTimer) {
        clearTimeout(this.gcTimer);
      }
    }
  }

  /**
   * 基于大纲章节生成研究问题
   */
  const generateChapterQuestions = useCallback(async (chapter: OutlineChapter): Promise<string[]> => {
    const { thinkingModel } = getModel();
    
    const prompt = `基于以下章节信息，生成5-8个具体的研究问题：

章节标题：${chapter.title}
章节描述：${chapter.description}
研究要点：
${chapter.researchPoints.map(point => `- ${point}`).join('\n')}

要求：
1. 问题要具体、可研究
2. 覆盖章节的所有研究要点
3. 适合网络搜索和资料分析
4. 每个问题都应该能产生有价值的研究结果

请只输出问题列表，每行一个问题：`;

    try {
      const result = await generateText({
        model: await createModelProvider(thinkingModel),
        system: getSystemPrompt(),
        prompt: [prompt, getResponseLanguagePrompt()].join('\n\n'),
      });

      const questions = result.text
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0 && !line.startsWith('#'))
        .map(line => line.replace(/^\d+\.\s*/, '').replace(/^-\s*/, ''))
        .filter(line => line.length > 10);

      return questions.slice(0, 8); // 最多8个问题
    } catch (error) {
      console.error('生成章节问题失败:', error);
      return chapter.researchPoints; // 降级到研究要点
    }
  }, []);

  /**
   * 为单个章节进行完整的研究流程（修复版本，确保任务完全完成）
   */
  const researchChapter = useCallback(async (chapter: OutlineChapter, chapterIndex: number) => {

    // 🔥 修复：在try块外声明变量
    const originalReportPlan = taskStore.reportPlan;
    const originalTasksCount = taskStore.tasks.length;
    
    try {
      setStatus(`第${chapterIndex + 1}章：${chapter.title} - 生成研究主题...`);

      // 🔥 关键修复：在调用deepResearch之前设置章节上下文，包含原始研究主题
      const originalResearchTopic = taskStore.question || taskStore.requirement || '未设置研究主题';
      taskStore.setCurrentChapterContext({
        chapterIndex: chapterIndex,
        chapterTitle: chapter.title,
        chapterDescription: chapter.description,
        researchPoints: chapter.researchPoints,
        originalResearchTopic: originalResearchTopic // 🔥 新增：传递原始研究主题
      });

      // 临时设置章节特定的研究计划，包含原始研究主题
      const chapterPlan = `# 第${chapterIndex + 1}章：${chapter.title}

## 原始研究主题
${originalResearchTopic}

## 章节描述
${chapter.description}

## 研究要点
${chapter.researchPoints.map(point => `- ${point}`).join('\n')}

## 研究目标
基于原始研究主题"${originalResearchTopic}"，针对第${chapterIndex + 1}章"${chapter.title}"进行深度研究，收集相关信息和数据。所有查询都应该与原始研究主题紧密相关。`;

      taskStore.updateReportPlan(chapterPlan);
      
      // 🔥 修复：调用deepResearch并等待完成
      setStatus(`第${chapterIndex + 1}章：${chapter.title} - 执行深度研究...`);

      await deepResearch();

      // 🔥 移除旧的后置标识添加逻辑，因为现在任务在生成时就应该带有章节标识
      
      // 🔥 关键修复：等待所有新任务完成

      let allCompleted = false;
      let waitCount = 0;
      const maxWait = 300; // 最多等待5分钟
      
      while (!allCompleted && waitCount < maxWait) {
        const currentTasks = taskStore.tasks.slice(originalTasksCount);
        const unfinishedTasks = currentTasks.filter(task => task.state !== "completed" && task.state !== "failed");

        // 🔥 修复：如果有任务且都已完成，或者没有新任务生成，都应该退出等待
        if (currentTasks.length === 0) {

          allCompleted = true;
        } else if (unfinishedTasks.length === 0) {

          allCompleted = true;
        } else {

          setStatus(`第${chapterIndex + 1}章：${chapter.title} - 还有 ${unfinishedTasks.length} 个任务进行中...`);
          await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
          waitCount++;
        }
      }
      
      if (!allCompleted) {

      }

      // 恢复原始的reportPlan
      taskStore.updateReportPlan(originalReportPlan);
      
      // 🔥 清除章节上下文
      taskStore.clearCurrentChapterContext();
      
      setStatus(`✅ 第${chapterIndex + 1}章研究完成：${chapter.title}`);
      
      // 更新进度显示
      const finalTasksAfterCompletion = taskStore.tasks.slice(originalTasksCount);
      const completedTasksCount = finalTasksAfterCompletion.filter(task => task.state === "completed").length;
      const chapterCompletedDisplay = `✅ 第${chapterIndex + 1}章研究完成：${chapter.title}\n收集的信息数量：${completedTasksCount} 项`;
      taskStore.setChapterResearchProgress(chapterCompletedDisplay);
      
    } catch (error) {
      console.error(`第${chapterIndex + 1}章研究失败:`, error);
      setStatus(`❌ 第${chapterIndex + 1}章研究失败: ${error?.toString()}`);
      // 恢复原始的reportPlan（如果已定义）
      if (originalReportPlan !== undefined) {
        taskStore.updateReportPlan(originalReportPlan);
      }
      // 🔥 确保在异常情况下也清除章节上下文
      taskStore.clearCurrentChapterContext();
    }
  }, [taskStore, deepResearch]);

  /**
   * 启动大纲驱动的完整研究流程
   */
  const startOutlineDrivenResearch = useCallback(async (outline: ResearchOutline) => {
    if (!outline || outline.chapters.length === 0) {
      throw new Error('大纲无效或没有章节');
    }

    setIsResearching(true);
    setCurrentChapter(0);
    
    try {
      taskStore.setResearchOutline(outline);
      taskStore.setOutlineDriven(true);
      
      taskStore.updateWritingProgress({
        currentChapter: 0,
        currentSection: 0,
        completedChapters: [],
        completedSections: [],
        totalProgress: 0,
        startTime: new Date(),
      });

      const overview = `# 分章节预写模式\n\n**总览:**\n${outline.chapters.filter((c: OutlineChapter) => c.enabled).map((c: OutlineChapter, i: number) => `${i + 1}. ${c.title}`).join('\n')}`;
      taskStore.setChapterResearchProgress(overview);

      if (!taskStore.reportPlan || taskStore.reportPlan.trim().length === 0) {
        setStatus('生成报告计划...');
        await writeReportPlan();
      }

      const enabledChapters = outline.chapters.filter((chapter: OutlineChapter) => chapter.enabled);

      for (let i = 0; i < enabledChapters.length; i++) {
        const chapter = enabledChapters[i];
        const chapterIndex = outline.chapters.findIndex((c: OutlineChapter) => c.id === chapter.id);

        setCurrentChapter(chapterIndex);
        setStatus(`开始预写第${chapterIndex + 1}章：${chapter.title}... (${i + 1}/${enabledChapters.length})`);
        
        // 🔥 修复：更新当前章节进度
        const currentProgress = Math.round((i / enabledChapters.length) * 100);
        taskStore.updateWritingProgress({
          currentChapter: chapterIndex,
          totalProgress: currentProgress,
          completedChapters: taskStore.writingProgress?.completedChapters || []
        });

        await researchChapter(chapter, chapterIndex);

        // 🔥 修复：研究完成后更新进度
        const completedProgress = Math.round(((i + 1) / enabledChapters.length) * 100);
        taskStore.updateWritingProgress({
          currentChapter: chapterIndex,
          totalProgress: completedProgress,
          completedChapters: [...(taskStore.writingProgress?.completedChapters || []), chapterIndex]
        });

        // 🔥 修复：更新综合进度显示
        const progressSummary = `# 分章节研究进度\n\n**总体进度：** ${completedProgress}% (${i + 1}/${enabledChapters.length})\n\n**已完成章节：**\n${enabledChapters.slice(0, i + 1).map((c, idx) => `✅ 第${idx + 1}章：${c.title}`).join('\n')}\n\n${i + 1 < enabledChapters.length ? `**下一章节：**\n⏳ 第${i + 2}章：${enabledChapters[i + 1].title}` : '🎉 所有章节研究完成！'}`;
        taskStore.setChapterResearchProgress(progressSummary);
        
        if (i < enabledChapters.length - 1) {
          setStatus(`第${chapterIndex + 1}章预写完成，准备下一章节...`);

          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }

      taskStore.updateWritingProgress({
        totalProgress: 100,
        currentChapter: outline.chapters.length,
      });

      const completionSummary = `# 🎉 分章节预写完成！\n\n所有章节预写已完成，可以开始写作。`;
      taskStore.setChapterResearchProgress(completionSummary);
      setStatus(''); // 清空状态，避免显示"研究进行中"
      
    } catch (error) {
      console.error('大纲驱动预写失败:', error);
      setStatus(`❌ 预写失败: ${error?.toString()}`);
      throw error;
    } finally {
      setIsResearching(false);
    }
  }, [writeReportPlan, researchChapter, taskStore, setStatus, setIsResearching, setCurrentChapter]);

  /**
   * 🔥 全新增强：集成上下文管理的大纲驱动报告写作
   * 支持智能子章节拆分，使用大章节研究内容
   */
  const writeOutlineDrivenReportWithContext = useCallback(async () => {
    const {
      reportPlan,
      tasks: learnings,
      sources,
      resources,
      requirement,
      researchOutline,
      question,
    } = useTaskStore.getState();
    
    // 🔥 修复数据传递断层：如果requirement为空但question有内容，将question复制到requirement
    // 确保AI能够获得用户的完整研究主题信息，正确识别公司名称
    const originalResearchTopic = question || requirement || '未设置研究主题';
    let effectiveRequirement = requirement;
    if (!requirement && question) {
      effectiveRequirement = question;
      useTaskStore.getState().setRequirement(question);
    }
    
    // 🔥 重要：保存原始研究主题，确保不会被清空
    const originalQuestion = question;

    if (!researchOutline || researchOutline.chapters.length === 0) {
      throw new Error('没有可用的研究大纲');
    }

    const { thinkingModel } = getModel();
    
    // 获取写作配置和延迟设置
    const outlineStore = useOutlineStore.getState();
    const writingConfig = outlineStore.currentOutline?.writingConfig || getEffectiveWritingConfig();
    console.log('📋 Outline-driven research 配置来源:', outlineStore.currentOutline ? '大纲配置' : '默认配置');
    console.log('📋 Outline-driven research 使用的写作配置:', writingConfig);
    console.log('🎨 当前写作风格:', writingConfig.style);
    console.log('🌍 语言要求:', writingConfig.languageRequirements);
    console.log('⚙️ 自定义指令:', writingConfig.customInstructions || '无');
    if (outlineStore.currentOutline) {
      console.log('📖 当前大纲名称:', outlineStore.currentOutline.title);
      console.log('📚 大纲章节数:', outlineStore.currentOutline.chapters.length);
    }
    const { chapterDelay, sectionDelay } = getConfiguredDelays(writingConfig);

    try {
      setStatus("开始生成具有上下文记忆的大纲驱动报告...");

      // 创建内存优化器和标签处理器
      const memoryOptimizer = new MemoryOptimizer();
      const thinkTagStreamProcessor = new ThinkTagStreamProcessor();

      // 报告构建器
      const reportBuilder = new ContextAwareReportBuilder((content) => {
        useTaskStore.getState().updateFinalReport(content);
      });

      // 初始化清理
      useTaskStore.getState().updateFinalReport("");
      useTaskStore.getState().setSources([]);

      // 生成报告标题
      setStatus(t("research.common.writingTitle"));
      
      const titleResult = await generateText({
        model: await createModelProvider(thinkingModel),
        system: getSystemPrompt(),
        prompt: [
          writeReportTitlePrompt(reportPlan, learnings.map(task => task.learning || ''), question, requirement),
          getResponseLanguagePrompt(),
        ].join("\n\n"),
      });
      
      // 清理标题
      const rawTitle = titleResult.text.trim();
      const cleanedTitleResult = removeThinkTags(rawTitle);
      const cleanTitle = cleanedTitleResult.content.trim();
      
      // 设置标题到store
      useTaskStore.getState().setTitle(cleanTitle);
      
      // 设置基础内容
      const baseContent = `# ${cleanTitle}\n\n` +
        `**报告日期：** ${new Date().toLocaleDateString('zh-CN')}\n\n` +
        `**目标读者：** 投资决策机构\n\n` +
        `---\n\n`;
      
      reportBuilder.append(baseContent);

      // 初始化上下文管理器
      initializeContextManager(cleanTitle);

      // 存储各章节内容
      const chapterContents: { [key: number]: string[] } = {};
      const completedChapters: { [key: number]: string } = {};

      // 🔥 核心逻辑：基于大纲进行智能写作
      const enabledChapters = researchOutline.chapters.filter((chapter: any) => chapter.enabled);
      
      for (let i = 0; i < enabledChapters.length; i++) {
        const chapter = enabledChapters[i];
        const chapterIndex = researchOutline.chapters.findIndex((c: any) => c.id === chapter.id);
        
        chapterContents[chapterIndex] = [];

        // 🔥 关键：获取章节特定的研究内容（大章节级别）
        const chapterSpecificTasks = learnings.filter((task: any) => 
          task.query && (
            task.query.includes(`[第${chapterIndex + 1}章:`) ||
            task.query.includes(`第${chapterIndex + 1}章`) ||
            task.query.includes(chapter.title)
          )
        );
        
        const relevantTasks = chapterSpecificTasks.length > 0 ? chapterSpecificTasks : learnings;
        const chapterLearnings = relevantTasks.map((task: any) => task.learning || '');

        // 🔥 智能判断：是否有子章节需要拆分写作
        const hasSubChapters = chapter.sections && chapter.sections.length > 0;

        if (hasSubChapters) {
          // 🔥 子章节拆分写作逻辑
          setStatus(`正在生成第${chapterIndex + 1}章：${chapter.title} - 拆分为${chapter.sections.length}个子章节...`);

          // 添加主章节标题
          const chapterTitleContent = `\n\n## 第${chapterIndex + 1}章：${chapter.title}\n\n`;
          reportBuilder.append(chapterTitleContent);

          // 逐个生成子章节
          for (let subIndex = 0; subIndex < chapter.sections.length; subIndex++) {
            const subChapter = chapter.sections[subIndex];
           
            setStatus(`正在生成第${chapterIndex + 1}章第${subIndex + 1}节：${subChapter.title}...`);

            // 🔥 增强需求信息，包含原始研究主题
            const enhancedRequirement = originalResearchTopic 
              ? `原始研究主题：${originalResearchTopic}\n\n章节需求：${effectiveRequirement}`
              : effectiveRequirement;

            // 🔥 关键：使用专业的子章节提示词模板，传入完整的研究数据和写作配置
            const subChapterPrompt = generateSubChapterPrompt(
              chapterIndex + 1, // 章节编号 (1-6)
              subIndex, // 子章节索引 (0-based)
              reportPlan,
              chapterLearnings,
              sources || [], // 网络来源
              resources || [], // 本地资源  
              [], // images - 暂时为空，后续可扩展
              enhancedRequirement, // 🔥 使用增强的需求信息，包含原始研究主题
              writingConfig // 🎯 传递写作配置
            );
            
            console.log(`🎯 子章节生成应用写作配置 - 第${chapterIndex + 1}章第${subIndex + 1}节:`, writingConfig.style, writingConfig.languageRequirements);

            // 添加上下文信息（如果有已完成的章节）
            let enhancedPrompt = subChapterPrompt;
            if (contextManagerRef.current && Object.keys(completedChapters).length > 0) {
              enhancedPrompt = contextManagerRef.current.enhanceSubChapterPrompt(
                chapterIndex,
                subIndex,
                subChapterPrompt
              );
            }

            // 生成子章节内容  
            const subChapterResult = streamText({
              model: await createModelProvider(thinkingModel),
              system: [getSystemPrompt(), outputGuidelinesPrompt].join("\n\n"),
              prompt: [enhancedPrompt, getResponseLanguagePrompt()].join("\n\n"),
              onError: (error) => {
                console.error("生成子章节时出错:", error);
                throw error;
              },
            });

            let subChapterContent = "";
            let reasoning = "";

            // 处理流式输出
            for await (const part of subChapterResult.fullStream) {
              if (part.type === "text-delta") {
                thinkTagStreamProcessor.processChunk(
                  part.textDelta,
                  (data) => {
                    subChapterContent += data;
                    reportBuilder.updateCurrentChapter(subChapterContent + "\n\n");
                  },
                  (data) => {
                    reasoning += data;
                  }
                );
              } else if (part.type === "reasoning") {
                reasoning += part.textDelta;
              }
            }

            // 格式化子章节内容
            const formattedSubChapter = fixFormat(subChapterContent, {
              fixTitles: false,
              fixBold: true,
              fixSpacing: false,
              fixCitations: false,
              maxBoldPerParagraph: 2
            }).content;

            // 存储子章节内容
            chapterContents[chapterIndex].push(formattedSubChapter);
            
            // 添加到报告构建器
            reportBuilder.append(formattedSubChapter + "\n\n");

            // 重置状态
            thinkTagStreamProcessor.end();
            reasoning = "";
            subChapterContent = "";
            memoryOptimizer.scheduleCleanup();

            // 子章节间延迟（使用配置的延迟）
            if (subIndex < chapter.sections.length - 1) {
              await new Promise(resolve => setTimeout(resolve, sectionDelay));
            }
          }

          // 清空当前章节内容，避免重复显示
          reportBuilder.updateCurrentChapter("");

          // 整个主章节完成，记录到completedChapters
          const fullChapterContent = chapterContents[chapterIndex].join('\n\n');
          completedChapters[chapterIndex] = fullChapterContent;

        } else {
          // 🔥 大章节直接写作逻辑（无子章节）
          setStatus(`正在生成第${chapterIndex + 1}章：${chapter.title}...`);

          // 添加章节标题
          const chapterTitleContent = `\n\n## 第${chapterIndex + 1}章：${chapter.title}\n\n`;
          reportBuilder.append(chapterTitleContent);

          // 使用增强的章节写作提示词，包含写作配置
          const { applyWritingConfigToPrompt, generateWritingStylePrompt } = await import('@/utils/writing-config-helper');
          
          // 🔥 增强需求信息，包含原始研究主题
          const enhancedChapterRequirement = originalResearchTopic 
            ? `原始研究主题：${originalResearchTopic}\n\n章节需求：${effectiveRequirement}`
            : effectiveRequirement;

          const baseChapterPrompt = `
请为研究报告撰写第${chapterIndex + 1}章内容：

**原始研究主题：** ${originalResearchTopic}

**章节标题：** ${chapter.title}
**章节描述：** ${chapter.description}

**研究要点：**
${chapter.researchPoints.map((point: string) => `- ${point}`).join('\n')}

**字数要求：** ${chapter.wordCount.min}-${chapter.wordCount.max}字

**章节特定研究内容：**
${chapterLearnings.length > 0 ? chapterLearnings.join('\n\n---\n\n') : '使用通用研究内容，请重点关注与本章节相关的信息'}

**报告计划：**
${reportPlan}

**研究需求：**
${enhancedChapterRequirement}

**基础写作要求：**
1. 严格按照章节大纲结构组织内容
2. 重点使用与本章节相关的研究发现
3. 确保内容与章节标题和描述高度匹配
4. 保持逻辑清晰，论证充分
5. 字数控制在${chapter.wordCount.min}-${chapter.wordCount.max}字范围内
`;
          
          // 应用写作配置到提示词
          const chapterPrompt = applyWritingConfigToPrompt(baseChapterPrompt, writingConfig);
          console.log('🎯 大纲驱动章节生成应用写作配置:', writingConfig.style, writingConfig.languageRequirements);

          // 添加上下文信息（如果有已完成的章节）
          let enhancedPrompt = chapterPrompt;
          if (contextManagerRef.current && Object.keys(completedChapters).length > 0) {
            enhancedPrompt = contextManagerRef.current.enhanceSubChapterPrompt(
              chapterIndex,
              0, // 子章节索引，这里简化为0
              chapterPrompt
            );
          }

          // 生成章节内容  
          const chapterResult = streamText({
            model: await createModelProvider(thinkingModel),
            system: [getSystemPrompt(), outputGuidelinesPrompt].join("\n\n"),
            prompt: [enhancedPrompt, getResponseLanguagePrompt()].join("\n\n"),
            onError: (error) => {
              console.error("生成章节时出错:", error);
              throw error;
            },
          });

          let chapterContent = "";
          let reasoning = "";
          
          // 处理流式输出
          for await (const part of chapterResult.fullStream) {
            if (part.type === "text-delta") {
              thinkTagStreamProcessor.processChunk(
                part.textDelta,
                (data) => {
                  chapterContent += data;
                  reportBuilder.updateCurrentChapter(chapterContent + "\n\n");
                },
                (data) => {
                  reasoning += data;
                }
              );
            } else if (part.type === "reasoning") {
              reasoning += part.textDelta;
            }
          }

          // 格式化章节内容
          const formattedChapter = fixFormat(chapterContent, {
            fixTitles: false,
            fixBold: true,
            fixSpacing: false,
            fixCitations: false,
            maxBoldPerParagraph: 2
          }).content;

          // 存储章节内容
          chapterContents[chapterIndex] = [formattedChapter];
          completedChapters[chapterIndex] = formattedChapter;
          
          // 添加到报告构建器
          reportBuilder.append(formattedChapter + "\n\n");

          // 清空当前章节内容，避免重复显示
          reportBuilder.updateCurrentChapter("");

          // 重置状态
          thinkTagStreamProcessor.end();
          reasoning = "";
          chapterContent = "";
          memoryOptimizer.scheduleCleanup();
        }

        // 🔥 使用长程上下文管理器处理章节完成
        if (contextManagerRef.current) {
          try {
            const modelProvider = await createModelProvider(thinkingModel);
            await contextManagerRef.current.processChapterCompletion(
              chapterIndex,
              chapter.title,
              completedChapters[chapterIndex],
              modelProvider
            );
          } catch (error) {
            console.warn(`处理第${chapterIndex + 1}章摘要提取时出错:`, error);
          }
        }

        // 章节间延迟（使用配置的延迟）
        if (i < enabledChapters.length - 1) {
          await new Promise(resolve => setTimeout(resolve, chapterDelay));
        }
      }

      // 🔥 统一处理本地资源引用链接（修复引用编号不一致问题）
      setStatus("正在统一处理文档中的引用链接...");
      try {
        const currentContent = reportBuilder.toString();
        console.log('🔗 开始统一处理大纲写作的引用链接');
        console.log('📚 可用资源数量:', resources?.length || 0);
        
        // 内联实现引用处理函数，确保与其他地方的逻辑一致
        const processLocalResourceCitations = async (content: string, resources: any[]) => {
          if (!resources || resources.length === 0) return content;
          
          try {
            const configResponse = await fetch('/api/config');
            let locationKnowledgePath = '';
            
            if (configResponse.ok) {
              const config = await configResponse.json();
              locationKnowledgePath = config.locationKnowledgePath || '';
            }
            
            if (!locationKnowledgePath) return content;
            
            const normalizedBaseUrl = locationKnowledgePath.endsWith('/') 
              ? locationKnowledgePath 
              : locationKnowledgePath + '/';
            
            function getOriginalFileName(fileName: string): string {
              const match = fileName.match(/^(.+)-\d+\.(.+)$/);
              if (match) {
                return `${match[1]}.${match[2]}`;
              }
              return fileName;
            }
            
            let processedContent = content;
            
            // 首先处理多引用格式 [L-1, L-2, L-9, L-14, L-16]
            processedContent = processedContent.replace(/\[L-(\d+(?:,\s*L-\d+)*)\]/g, (match: string, citationsStr: string) => {
              const citations = citationsStr.split(/,\s*/).map((part: string) => part.replace(/L-/, ''));
              
              const processedCitations = citations.map((indexStr: string) => {
                const index = parseInt(indexStr) - 1;
                
                if (index >= 0 && index < resources.length) {
                  const resource = resources[index];
                  const originalFileName = getOriginalFileName(resource.name);
                  const resourceUrl = normalizedBaseUrl + encodeURIComponent(originalFileName);
                  return `[[L-${indexStr}]](${resourceUrl})`;
                }
                
                return `[L-${indexStr}]`;
              });
              
              return processedCitations.join(', ');
            });
            
            // 处理单方括号格式 [L-数字]
            processedContent = processedContent.replace(/(?<!\[)\[L-(\d+)\](?!\]|\()/g, (match: string, indexStr: string) => {
              const index = parseInt(indexStr) - 1;
              
              if (index >= 0 && index < resources.length) {
                const resource = resources[index];
                const originalFileName = getOriginalFileName(resource.name);
                const resourceUrl = normalizedBaseUrl + encodeURIComponent(originalFileName);
                return `[[L-${indexStr}]](${resourceUrl})`;
              }
              
              console.warn(`⚠️ 大纲写作引用索引无效: [L-${indexStr}], 索引=${index}, 资源总数=${resources.length}`);
              return match;
            });
            
            // 处理双方括号格式 [[L-数字]]（但不处理已有链接）
            processedContent = processedContent.replace(/\[\[L-(\d+)\]\](?!\()/g, (match: string, indexStr: string) => {
              const index = parseInt(indexStr) - 1;
              
              if (index >= 0 && index < resources.length) {
                const resource = resources[index];
                const originalFileName = getOriginalFileName(resource.name);
                const resourceUrl = normalizedBaseUrl + encodeURIComponent(originalFileName);
                return `[[L-${indexStr}]](${resourceUrl})`;
              }
              
              console.warn(`⚠️ 大纲写作双方括号引用索引无效: [[L-${indexStr}]], 索引=${index}, 资源总数=${resources.length}`);
              return match;
            });
            
            return processedContent;
          } catch (error) {
            console.warn('处理本地资源引用失败:', error);
            return content;
          }
        };
        
        let processedContent = await processLocalResourceCitations(currentContent, resources || []);
        
        // 🔥 过滤掉文章中间的参考文献部分，确保只在结尾有一个统一的参考资源列表
        console.log('🔧 清理文章中间的参考文献部分...');
        processedContent = processedContent
          // 移除章节中的参考文献标题
          .replace(/\n#{1,6}\s*参考文献\s*\n/g, '\n')
          .replace(/\n#{1,6}\s*参考资料\s*\n/g, '\n')
          .replace(/\n#{1,6}\s*参考资源\s*\n/g, '\n')
          .replace(/\n#{1,6}\s*资料来源\s*\n/g, '\n')
          .replace(/\n#{1,6}\s*References\s*\n/g, '\n')
          // 移除单独的资源引用行（非结尾的）
          .replace(/\n\[L-\d+\]:\s*[^\n]+\n(?!\n*(?:##\s*参考资源|$))/g, '\n')
          // 移除多余的空行
          .replace(/\n{3,}/g, '\n\n');
        
        console.log('✅ 文章中间的参考文献部分清理完成');
        
        // 更新报告构建器内容（使用公共方法）
        if (reportBuilder && typeof reportBuilder.replaceContent === 'function') {
          // 使用公共方法替换内容
          reportBuilder.replaceContent(processedContent);
        }
        
        console.log('✅ 大纲写作引用链接处理完成');
      } catch (error) {
        console.warn('统一处理引用链接时出错:', error);
      }

      // 🔥 处理本地资源引用列表
      await addLocalResourceReferences(reportBuilder, resources);

      // 🔥 AI验证（如果启用）
      if (isChapterValidationEnabled()) {
        setStatus("正在验证报告质量...");
        try {
          const validationResult = await validateContentWithAI(
            reportBuilder.toString(),
            {
              mode: 'fulltext',
              localKnowledge: learnings.map(task => task.learning || '').join('\n\n'),
            }
          );
          
          if (validationResult.hasChanges) {
            console.warn("报告验证发现问题，已自动修正");
          }
        } catch (error) {
          console.warn("AI验证失败:", error);
        }
      }

      // 🔥 双模型验证（如果启用）
      if (isDualModelValidationEnabled()) {
        setStatus("正在进行双模型验证...");
        try {
          const dualValidationResult = await validateContentWithDualModel(
            reportBuilder.toString(),
            {
              mode: 'fulltext',
              localKnowledge: learnings.map(task => task.learning || '').join('\n\n'),
            }
          );
          
          if (dualValidationResult.hasChanges) {
            console.warn("双模型验证发现问题，已自动修正");
          }
        } catch (error) {
          console.warn("双模型验证失败:", error);
        }
      }

      // 清理资源
      reportBuilder.destroy();
      memoryOptimizer.destroy();

      // 保存到历史记录
      const id = save(taskStore.backup());
      taskStore.setId(id);
      setId(id);

      setStatus("🎉 大纲驱动报告生成完成！");

      // 在函数最后（return语句之前）添加以下代码，确保研究主题被保留
      // 确保研究主题不会被清空，即使其他状态发生变化
      if (originalQuestion) {
        useTaskStore.getState().setQuestion(originalQuestion);
      }

    } catch (error) {
      console.error("生成大纲驱动报告失败:", error);
      setStatus(`❌ 报告生成失败: ${error?.toString()}`);
      throw error;
    }
  }, [save, createModelProvider, getModel, t]);

  // 🔥 处理本地资源引用
  async function addLocalResourceReferences(
    reportBuilder: any, 
    resources: any[]
  ): Promise<void> {
    if (!resources || resources.length === 0) return;

    try {
      setStatus("添加本地资源引用...");

      const resourceSection = "\n\n## 参考资源\n\n";
      reportBuilder.append(resourceSection);

      for (const resource of resources) {
        if (resource && resource.name) {
          const originalFileName = getOriginalFileName(resource.name);
          const reference = `- **${originalFileName}**: ${resource.summary || '本地资源文件'}\n`;
          reportBuilder.append(reference);
        }
      }

      reportBuilder.append("\n");
    } catch (error) {
      console.warn("添加本地资源引用时出错:", error);
    }

    function getOriginalFileName(fileName: string): string {
      const parts = fileName.split('_');
      if (parts.length > 2) {
        return parts.slice(2).join('_');
      }
      return fileName;
    }
  }

  // 🔥 保持原有的向后兼容方法
  const writeOutlineDrivenReport = useCallback(async () => {
    // 直接调用增强版的方法
    return writeOutlineDrivenReportWithContext();
  }, [writeOutlineDrivenReportWithContext]);

  /**
   * 为特定章节生成内容（使用章节特定的研究上下文）
   */
  const writeChapterContent = useCallback(async (chapterIndex: number) => {
    const outline = taskStore.researchOutline;
    if (!outline || !outline.chapters[chapterIndex]) {
      throw new Error('章节不存在');
    }

    const chapter = outline.chapters[chapterIndex];
    setStatus(`正在生成第${chapterIndex + 1}章：${chapter.title}...`);

    try {
      const { thinkingModel } = getModel();
      const { 
        reportPlan,
        tasks,
        requirement,
        resources,
      } = taskStore;

      // 🔥 关键修正：获取章节特定的研究内容
      const chapterSpecificTasks = tasks.filter(task => 
        task.query.includes(`[第${chapterIndex + 1}章:`) ||
        task.query.includes(`第${chapterIndex + 1}章`) ||
        task.query.includes(chapter.title)
      );
      
      // 如果没有章节特定的研究内容，使用所有研究内容但添加章节上下文提示
      const relevantTasks = chapterSpecificTasks.length > 0 ? chapterSpecificTasks : tasks;
      const learnings = relevantTasks.map((item) => item.learning).filter(Boolean);
      const sources = relevantTasks.flatMap((item) => item.sources || []);
      // const images = relevantTasks.flatMap((item) => item.images || []);

      // 🔥 构建章节特定的上下文提示（不包含子章节，因为研究目前只考虑大章节）
      const chapterContext = `
## 第${chapterIndex + 1}章写作上下文

**章节标题：** ${chapter.title}
**章节描述：** ${chapter.description}

**研究要点：**
${chapter.researchPoints.map((point: string) => `- ${point}`).join('\n')}

**字数要求：** ${chapter.wordCount.min}-${chapter.wordCount.max}字

**章节特定研究内容：**
${learnings.length > 0 ? learnings.join('\n\n---\n\n') : '使用通用研究内容，请重点关注与本章节相关的信息'}
`;

      // 使用通用的章节写作提示词，结合章节特定上下文和写作配置
      const { applyWritingConfigToPrompt } = await import('@/utils/writing-config-helper');
      const outlineStore = useOutlineStore.getState();
      const currentWritingConfig = outlineStore.currentOutline?.writingConfig || getEffectiveWritingConfig();
      
      const basePrompt = `
请基于以下信息为研究报告撰写第${chapterIndex + 1}章内容：

${chapterContext}

**报告计划：**
${reportPlan}

**研究需求：**
${requirement}

**可用资源：**
${JSON.stringify(resources, null, 2)}

**参考来源：**
${JSON.stringify(sources, null, 2)}

**基础写作要求：**
1. 严格按照章节大纲结构组织内容
2. 重点使用与本章节相关的研究发现
3. 确保内容与章节标题和描述高度匹配
4. 保持逻辑清晰，论证充分
5. 字数控制在${chapter.wordCount.min}-${chapter.wordCount.max}字范围内
`;

      // 应用写作配置
      const prompt = applyWritingConfigToPrompt(basePrompt, currentWritingConfig);
      console.log(`🎯 章节写作函数应用写作配置 - 第${chapterIndex + 1}章:`, currentWritingConfig.style, currentWritingConfig.languageRequirements);

      const result = streamText({
        model: await createModelProvider(thinkingModel),
        system: getSystemPrompt(),
        prompt: [prompt, getResponseLanguagePrompt()].join('\n\n'),
      });

      let content = '';
      for await (const part of result.fullStream) {
        if (part.type === 'text-delta') {
          content += part.textDelta;
          // 实时更新报告内容
          taskStore.updateFinalReport(taskStore.finalReport + part.textDelta);
        }
      }

      // 更新章节完成状态
      taskStore.updateWritingProgress({
        completedChapters: [...(taskStore.writingProgress?.completedChapters || []), chapterIndex]
      });

      setStatus(`第${chapterIndex + 1}章生成完成`);
      return content;

    } catch (error) {
      console.error(`生成第${chapterIndex + 1}章失败:`, error);
      throw error;
    }
  }, [taskStore]);

  /**
   * 获取研究进度统计
   */
  const getResearchStats = useCallback(() => {
    const outline = taskStore.researchOutline;
    const progress = taskStore.writingProgress;
    
    if (!outline) {
      return {
        totalChapters: 0,
        completedChapters: 0,
        currentChapter: 0,
        totalProgress: 0,
        isComplete: false,
      };
    }

    const totalChapters = outline.chapters.filter((c: OutlineChapter) => c.enabled).length;
    const completedChapters = progress?.completedChapters.length || 0;
    const currentChapter = progress?.currentChapter || 0;
    // 🔥 修复：使用writingProgress中的totalProgress，如果没有则计算
    const totalProgress = progress?.totalProgress || (totalChapters > 0 ? Math.round((completedChapters / totalChapters) * 100) : 0);

    return {
      totalChapters,
      completedChapters,
      currentChapter,
      totalProgress,
      isComplete: completedChapters >= totalChapters,
    };
  }, [taskStore]);

  // 🔥 上下文管理相关的辅助功能
  const getContextStats = () => {
    if (!contextManagerRef.current) return null;
    return contextManagerRef.current.getContextStats();
  };

  const getChapterConnectionAnalysis = () => {
    if (!contextManagerRef.current) return null;
    return contextManagerRef.current.getChapterConnectionAnalysis();
  };

  const exportContextData = () => {
    if (!contextManagerRef.current) return null;
    return contextManagerRef.current.exportContextData();
  };

  const clearContextCache = () => {
    if (contextManagerRef.current) {
      contextManagerRef.current.clearContext();
    }
  };

  return {
    // 状态
    status,
    isResearching,
    currentChapter,
    id,
    
    // 核心功能 - 🔥 增强版方法
    startOutlineDrivenResearch,
    writeOutlineDrivenReportWithContext,
    writeOutlineDrivenReport, // 向后兼容
    writeChapterContent,
    
    // 章节研究功能
    generateChapterQuestions,
    researchChapter,
    getResearchStats,
    
    // 🔥 上下文管理相关 - 新增功能
    getContextStats,
    getChapterConnectionAnalysis,
    exportContextData,
    clearContextCache,
    
    // 现有功能的引用
    askQuestions,
    writeReportPlan,
    deepResearch,
    writeFinalReport,
    writeChapterReport,
  };
} 