/**
 * 获取章节字数控制配置
 * @param chapterNum 章节编号 (1-6)
 * @returns 字数范围字符串，如"2800-3800字"
 */
export function getChapterWordCount(chapterNum: number): string {
  const minKey = `CHAPTER_${chapterNum}_WORD_MIN`;
  const maxKey = `CHAPTER_${chapterNum}_WORD_MAX`;
  
  const minWords = process.env[minKey] || getDefaultWordCount(chapterNum).min;
  const maxWords = process.env[maxKey] || getDefaultWordCount(chapterNum).max;
  
  return `${minWords}-${maxWords}字`;
}

/**
 * 获取默认字数设置
 */
function getDefaultWordCount(chapterNum: number): { min: number; max: number } {
  const defaults = {
    1: { min: 3800, max: 4000 }, // 商业模式
    2: { min: 3800, max: 4000 }, // 经营状况  
    3: { min: 3800, max: 4000 }, // 管理团队
    4: { min: 3800, max: 4000 }, // 治理结构
    5: { min: 3800, max: 4000 }, // 发展情况与投资回报
    6: { min: 3800, max: 4000 }  // 总结与评价
  };
  return defaults[chapterNum as keyof typeof defaults] || { min: 3800, max: 4000 };
}

export const systemInstruction = `You are an expert researcher. Today is {now}. Follow these instructions when responding:

- You may be asked to research subjects that is after your knowledge cutoff, assume the user is right when presented with news.
- The user is a highly experienced analyst, no need to simplify it, be as detailed as possible and make sure your response is correct.
- Be highly organized.
- Suggest solutions that I didn't think about.
- Be proactive and anticipate my needs.
- Treat me as an expert in all subject matter.
- Mistakes erode my trust, so be accurate and thorough.
- Provide detailed explanations, I'm comfortable with lots of detail.
- Value good arguments over authorities, the source is irrelevant.
- Consider new technologies and contrarian ideas, not just the conventional wisdom.
- You may use high levels of speculation or prediction, just flag it for me.`;

export const outputGuidelinesPrompt = `<OutputGuidelines>
Please strictly adhere to the following formatting guidelines when outputting text to ensure clarity, accuracy, and readability:

## Structured Content

-   **Clear Paragraphs**: Organize different ideas or topics using clear paragraphs.
-   **Titles and Subtitles**: Use different levels of headings to divide the content's hierarchical structure, ensuring logical clarity.

## Use of Markdown Syntax (if the platform supports it)

-   **Bold and Italics**: Use to emphasize keywords or concepts.
    -   For example: **Important Information** or *Emphasized Section*.
-   **Bulleted and Numbered Lists**: Use to list key points or steps.
    -   Unordered list:
        -   Item One
        -   Item Two
    -   Ordered list:
        1.  Step One
        2.  Step Two
-   **Code Blocks**: Use only for displaying code or content that needs to maintain its original format. Avoid placing mathematical formulas in code blocks.
    \`\`\`python
    def hello_world():
        print("Hello, World!")
    \`\`\`
-   **Quotes**: Use quote formatting when citing others' opinions or important information.
    > This is an example of a quote.
-   **Images**: Render images using markdown syntax.
    -   For example: ![image title](url)
-   **Mathematical Formulas and Tables**:
    -   **Mathematical Formulas**:
        -   **Display Formulas**: Use double dollar signs \`$$\` or backslash \`$$\` and \`$$\` to wrap formulas, making them display independently on a new line.
            For example:
            $$
            A = \\begin{pmatrix}
            3 & 2 & 1 \\\\
            3 & 1 & 5 \\\\
            3 & 2 & 3 \\\\
            \\end{pmatrix}
            $$
        -   **Inline Formulas**: Use single dollar signs \`$\` to wrap formulas, making them display within the text line.
            For example: The matrix $A = \\begin{pmatrix} 3 & 2 & 1 \\\\ 3 & 1 & 5 \\\\ 3 & 2 & 3 \\end{pmatrix}$ is a $3 \\times 3$ matrix.
    -   **Tables**: Use Markdown tables to display structured data, ensuring information is aligned and easy to compare.
        For example:

        | Name | Age | Occupation |
        |------|-----|------------|
        | John Doe | 28 | Engineer   |
        | Jane Smith | 34 | Designer   |

## 中文报告格式规范

**【核心】中国官方/正式报告写作风格（必须遵循）：**
请以中国官方/正式报告风格撰写：采用整段连贯叙述，避免分项符号和子标题罗列，用连接词自然过渡，确保逻辑严谨、语言精练庄重，将要点融入段落中阐述。运用"同时"、"此外"、"另一方面"、"综合而言"、"据此可见"、"由此观之"等连接词实现段落间的自然流畅过渡，体现正式报告的权威性和专业性。

**【重要】中文报告严格格式要求：**

**标题格式（不允许任何变动）：**
- 一级子标题：严格使用"1、2、3、"格式（中文数字+顿号）
- 二级子标题：严格使用"1.1、1.2、1.3"格式（阿拉伯数字+点号+顿号）
- 三级子标题：严格使用"1.1.1、1.1.2"格式
- 四级子标题：严格使用"1.1.1.1、1.1.1.2"格式

**引用规范（必须严格遵守）：**
- **本地资源引用：** 在分析时必须引用本地研究资料，使用[[L-1]]、[[L-2]]等格式
- **网络来源引用：** 引用网络资料时使用[[1]]、[[2]]等格式
- **引用密度控制：** 每段最多2-3个引用，避免过度引用影响阅读
- **引用位置：** 重要观点和数据后引用，避免在句子中间插入引用
- **引用示例：** "根据公司年报显示，该项技术已获得多项专利保护[[L-1]]。"

**格式规范：**
- **粗体使用：** 每段最多1-2处粗体，突出关键概念和重要数据
- **段落间距：** 不同要点间必须空行分隔，保持清晰结构
- **表格格式：** 使用标准Markdown表格，表格前后各空一行
- **列表格式：** 使用统一的项目符号（-），避免混用不同符号
- **数据展示：** 重要数据可使用表格或突出显示，但避免过度格式化

**🚫 严禁添加参考文献部分（重要）：**
- **绝对禁止**在章节末尾添加"参考文献"、"参考资料"、"参考资源"、"资料来源"等标题
- **绝对禁止**添加独立的引用列表（如[L-1]: xxx.pdf）
- **绝对禁止**添加"References"、"Bibliography"等英文参考文献部分
- **只允许**在正文中使用内联引用（如[[L-1]]、[[1]]）
- **参考文献将在整个报告完成后统一添加到文章结尾**

**【关键输出要求】：**
请直接输出符合以上格式要求的内容，不要包含任何格式说明、写作指导或元信息解释。只输出实际的内容。绝对不要添加参考文献部分。

## Fractions and Mathematical Representation

-   **Consistency**: Maintain consistency in the representation of fractions, prioritizing simplified forms.
    -   For example: Use \`-8/11\` instead of \`-16/22\`.
-   **Uniform Format**: Use either fraction or decimal forms consistently throughout the text, avoiding mixing them.

## Generate Mermaid

Generate a complete and accurate Mermaid diagram code based on the specified diagram type and data provided.
Ensure the code follows the Mermaid syntax and is properly structured for rendering without errors. 

### Steps

1. **Identify the diagram type**: Determine whether the user wants a flowchart, sequence diagram, class diagram, etc.
2. **Gather necessary data**: Collect information related to nodes, connections, and any specific style or configuration mentioned.
3. **Construct the Mermaid code**: Write the code based on the gathered data, ensuring that it follows the correct syntax for the chosen diagram type.
4. **Review for accuracy**: Check the code for any potential errors or formatting issues before finalizing.

### Output Format

Return the Mermaid diagram code as a plain text block. Format it as follows:
\`\`\`mermaid
<diagram type>
<diagram content>
\`\`\` 

For example:
\`\`\`mermaid
flowchart TD
A[Start] --> B(Stop)
\`\`\`

### Examples

- **Flowchart Example:**

\`\`\`mermaid
flowchart TD
A[Starting Point] --> B{Is it valid?}
B -->|Yes| C[Proceed]
B -->|No| D[Error]
\`\`\`

- **Sequence Diagram Example:**

\`\`\`mermaid
sequenceDiagram
Alice->>John: Hello John, how are you?
John-->>Alice: Great! How about you?
\`\`\`

**Important Notes**:

-   **Avoid placing mathematical formulas in code blocks**. Mathematical formulas should be displayed correctly in Markdown using LaTeX syntax.
-   **Ensure the correctness and formatting of mathematical formulas**, using appropriate symbols and environments to display complex mathematical expressions.
-   **When generating a mermaid diagram**, all text content must be wrapped in \`"\` syntax.

By strictly following the above formatting requirements, you can generate text that is clearly structured, accurate in content, uniformly formatted, and easy to read and understand, helping users more effectively obtain and understand the information they need.
</OutputGuidelines>`;

export const systemQuestionPrompt = `Given the following query from the user, ask at least 5 follow-up questions to clarify the research direction:

<QUERY>
{query}
</QUERY>

Questions need to be brief and concise. No need to output content that is irrelevant to the question.`;

export const guidelinesPrompt = `Integration guidelines:
<GUIDELINES>
- Ensure each section has a distinct purpose with no content overlap.
- Combine related concepts rather than separating them.
- CRITICAL: Every section MUST be directly relevant to the main topic.
- Avoid tangential or loosely related sections that don't directly address the core topic.
</GUIDELINES>`;

export const reportPlanPrompt = `Given the following query from the user:
<QUERY>
{query}
</QUERY>

Generate a list of sections for the report based on the topic and feedback.
Your plan should be tight and focused with NO overlapping sections or unnecessary filler. Each section needs a sentence summarizing its content.

${guidelinesPrompt}

Before submitting, review your structure to ensure it has no redundant sections and follows a logical flow.`;

export const serpQuerySchemaPrompt = `You MUST respond in **JSON** matching this **JSON schema**:

\`\`\`json
{outputSchema}
\`\`\`

Expected output:

\`\`\`json
[
  {
    query: "This is a sample query.",
    researchGoal: "This is the reason for the query."
  }
]
\`\`\``;

export const serpQueriesPrompt = `This is the report plan after user confirmation:
<PLAN>
{plan}
</PLAN>

Based on previous report plan, generate a list of SERP queries to further research the topic. Make sure each query is unique and not similar to each other.

${serpQuerySchemaPrompt}`;

export const queryResultPrompt = `Please use the following query to get the latest information via the web:
<QUERY>
{query}
</QUERY>

You need to organize the searched information according to the following requirements:
<RESEARCH_GOAL>
{researchGoal}
</RESEARCH_GOAL>

You need to think like a human researcher.
Generate a list of learnings from the search results.
Make sure each learning is unique and not similar to each other.
The learnings should be to the point, as detailed and information dense as possible.
Make sure to include any entities like people, places, companies, products, things, etc in the learnings, as well as any specific entities, metrics, numbers, and dates when available. The learnings will be used to research the topic further.`;

// 🔥 新增：离线模式的查询提示词，不包含联网相关内容
export const offlineQueryResultPrompt = `Please analyze and provide insights for the following research query:
<QUERY>
{query}
</QUERY>

You need to organize your analysis according to the following requirements:
<RESEARCH_GOAL>
{researchGoal}
</RESEARCH_GOAL>

You need to think like a human researcher and provide comprehensive analysis based on your knowledge.
Generate a list of insights and learnings about the query topic.
Make sure each insight is unique and not similar to each other.
The insights should be to the point, as detailed and information dense as possible.
Make sure to include any entities like people, places, companies, products, things, etc in the insights, as well as any specific entities, metrics, numbers, and dates when available from your knowledge base. The insights will be used to research the topic further.

Note: This analysis is based on your existing knowledge without web search capabilities.`;

export const citationRulesPrompt = `Citation Rules:

- Please cite the context at the end of sentences when appropriate.
- Please use the format of citation number [number] to reference the context in corresponding parts of your answer.
- If a sentence comes from multiple contexts, please list all relevant citation numbers, e.g., [1][2]. Remember not to group citations at the end but list them in the corresponding parts of your answer.`;

export const searchResultPrompt = `Given the following contexts from a SERP search for the query:
<QUERY>
{query}
</QUERY>

You need to organize the searched information according to the following requirements:
<RESEARCH_GOAL>
{researchGoal}
</RESEARCH_GOAL>

The following context from the SERP search:
<CONTEXT>
{context}
</CONTEXT>

You need to think like a human researcher.
Generate a list of learnings from the contexts.
Make sure each learning is unique and not similar to each other.
The learnings should be to the point, as detailed and information dense as possible.
Make sure to include any entities like people, places, companies, products, things, etc in the learnings, as well as any specific entities, metrics, numbers, and dates when available. The learnings will be used to research the topic further.`;

export const searchKnowledgeResultPrompt = `Given the following contents from a local knowledge base search for the query:
<QUERY>
{query}
</QUERY>

You need to organize the searched information according to the following requirements:
<RESEARCH_GOAL>
{researchGoal}
</RESEARCH_GOAL>

The following contexts from local knowledge base:
<CONTEXT>
{context}
</CONTEXT>

Citation Rules for Local Resources:
- ONLY cite content that directly comes from the provided local knowledge sources.
- Use [L-number] format where number corresponds EXACTLY to the content index provided.
- Content index 1 = [L-1], Content index 2 = [L-2], etc. The index MUST match the content order.
- Please cite the local knowledge sources at the end of sentences when appropriate.
- If a sentence comes from multiple local knowledge sources, please list all relevant citation numbers, e.g., [L-1][L-2].
- Do NOT generate references for content that comes from your general knowledge or reasoning.
- When unsure about the exact source, do not add any citation.
- Remember not to group citations at the end but list them in the corresponding parts of your answer.

You need to think like a human researcher.
Generate a list of learnings from the contents.
Make sure each learning is unique and not similar to each other.
The learnings should be to the point, as detailed and information dense as possible.
Make sure to include any entities like people, places, companies, products, things, etc in the learnings, as well as any specific entities, metrics, numbers, and dates when available. The learnings will be used to research the topic further.`;

export const reviewPrompt = `This is the report plan after user confirmation:
<PLAN>
{plan}
</PLAN>

Here are all the learnings from previous research:
<LEARNINGS>
{learnings}
</LEARNINGS>

This is the user's suggestion for research direction:
<SUGGESTION>
{suggestion}
</SUGGESTION>

Based on previous research and user research suggestions, determine whether further research is needed.
If further research is needed, list of follow-up SERP queries to research the topic further.
Make sure each query is unique and not similar to each other.
If you believe no further research is needed, you can output an empty queries.

${serpQuerySchemaPrompt}`;

export const finalReportCitationImagePrompt = `Image Rules:

- Images related to the paragraph content at the appropriate location in the article according to the image description.
- Include images using \`![Image Description](image_url)\` in a separate section.
- **Do not add any images at the end of the article.**`;

export const finalReportReferencesPrompt = `Citation Rules:

- Please cite research references at the end of your paragraphs when appropriate.
- Include references from both online sources and local resources.
- For online sources: Please use the reference format [number], to reference the sources link in corresponding parts of your answer.
- For local resources: Please use the reference format [L-number], to reference the local resources in corresponding parts of your answer (e.g., [L-1], [L-2]).
- If a paragraph comes from multiple references, please list all relevant citation numbers, e.g., [1][L-1][2]. Remember not to group citations at the end but list them in the corresponding parts of your answer. Control the number of footnotes.
- Do not have more than 3 reference links in a paragraph, and keep only the most relevant ones.
- **Do not add references at the end of the report.**`;

export const finalReportPrompt = `This is the report plan after user confirmation:
<PLAN>
{plan}
</PLAN>

Here are all the learnings from previous research:
<LEARNINGS>
{learnings}
</LEARNINGS>

Here are all the sources from previous research:
<SOURCES>
{sources}
</SOURCES>

Here are all the images from previous research:
<IMAGES>
{images}
</IMAGES>

Here are all the local resources:
<LOCAL_RESOURCES>
{localResources}
</LOCAL_RESOURCES>

Please write according to the user's writing requirements:
<REQUIREMENT>
{requirement}
</REQUIREMENT>

**【核心】中国官方/正式报告写作风格（必须遵循）：**
请以中国官方/正式报告风格撰写：采用整段连贯叙述，避免分项符号和子标题罗列，用连接词自然过渡，确保逻辑严谨、语言精练庄重，将要点融入段落中阐述。运用"同时"、"此外"、"另一方面"、"综合而言"、"据此可见"、"由此观之"等连接词实现段落间的自然流畅过渡，体现正式报告的权威性和专业性。

Write a final report based on the report plan using the learnings from research.
Make it as as detailed as possible, aim for 5 pages or more, the more the better, include ALL the learnings from research.
**Including meaningful images from the previous research in the report is very helpful.**

**🚫 IMPORTANT: Do NOT add reference sections in the middle of chapters:**
- Do NOT add "参考文献", "参考资料", "参考资源", "资料来源", "References", "Bibliography" sections within chapters
- Do NOT add standalone reference lists like [L-1]: filename.pdf within chapters
- Only use inline citations like [[L-1]], [[1]] within the text
- References will be added automatically at the end of the complete report

**Respond only the final report content, and no additional text before or after.**`;

export const rewritingPrompt = `You are tasked with re-writing the following text to markdown. Ensure you do not change the meaning or story behind the text. 

**Respond only the updated markdown text, and no additional text before or after.**`;

export const knowledgeGraphPrompt = `Based on the following article, please extract the key entities (e.g., names of people, places, organizations, concepts, events, etc.) and the main relationships between them, and then generate a Mermaid graph code that visualizes these entities and relationships.

## Output format requirements

1. Use Mermaid's graph TD (Top-Down) or graph LR (Left-Right) type.
2. Create a unique node ID for each identified entity (must use English letters or abbreviations as IDs), and display the full name or key description of the entity in the node shape (e.g., PersonA[Alice], OrgB[XYZ Company]).
3. Relationships are represented as edges with labels, and the labels indicate the type of relationship (e.g., A --> |"Relationship Type"| B).
4. Respond with ONLY the Mermaid code (including block), and no additional text before or after.
5. Please focus on the most core entities in the article and the most important relationships between them, and ensure that the generated graph is concise and easy to understand.
6. All text content **MUST** be wrapped in \`"\` syntax. (e.g., "Any Text Content")
7. You need to double-check that all content complies with Mermaid syntax, especially that all text needs to be wrapped in \`"\`.`;

// 在 chapterReportPrompts 之前添加严格格式约束和避重机制
export const strictFormatTemplate = `
**【核心】中国官方/正式报告写作风格（必须遵循）：**
请以中国官方/正式报告风格撰写：采用整段连贯叙述，避免分项符号和子标题罗列，用连接词自然过渡，确保逻辑严谨、语言精练庄重，将要点融入段落中阐述。运用"同时"、"此外"、"另一方面"、"综合而言"、"据此可见"、"由此观之"等连接词实现段落间的自然流畅过渡，体现正式报告的权威性和专业性。

**【重要】严格格式要求 - 必须完全遵守：**

**标题格式（不允许任何变动）：**
- 主标题：严格使用"{expectedTitle}"
- 一级子标题：严格使用"1、2、3、"格式（中文数字+顿号）
- 二级子标题：严格使用"1.1、1.2、1.3"格式（阿拉伯数字+点号+顿号）
- 三级子标题：严格使用"1.1.1、1.1.2"格式
- 四级子标题：严格使用"1.1.1.1、1.1.1.2"格式

**连贯叙述与数字列举使用原则（重要）：**
- **优先连贯叙述**：遵循中国官方报告传统，采用整段连贯叙述，运用"同时"、"此外"、"另一方面"等连接词自然过渡
- **谨慎使用数字列举**：仅在确实需要清晰对比或列举多个并列要素时使用"1、2、3、4"等数字标识
- **减少分点频率**：避免过度依赖数字列举，保持段落的自然流畅性和官方报告的庄重感
- **连接词运用**：多使用"综合而言"、"据此可见"、"由此观之"、"从...角度来看"等专业连接词
- **使用场景限制**：数字列举主要用于：财务数据对比、多维度分析总结、关键指标罗列等必要场景

**内容要求：**
- 保持专业、客观、深入的分析风格
- 确保内容聚焦，避免泛泛而谈
- 避免重复前面章节已分析的内容
- **优先使用连贯叙述**，减少数字列举使用频率，保持官方报告的权威性和流畅性

**引用规范（必须严格遵守）：**
- **本地资源引用：** 在分析时必须引用本地研究资料，使用[[L-1]]、[[L-2]]等格式
- **网络来源引用：** 引用网络资料时使用[[1]]、[[2]]等格式
- **引用密度控制：** 每段最多2-3个引用，避免过度引用影响阅读
- **引用位置：** 重要观点和数据后引用，避免在句子中间插入引用
- **引用示例：** "根据公司年报显示，该项技术已获得多项专利保护[[L-1]]。"

**格式规范：**
- **粗体使用：** 每段最多1-2处粗体，突出关键概念和重要数据
- **段落间距：** 不同要点间必须空行分隔，保持清晰结构
- **列举要点格式：** 使用"1、2、3、"格式进行要点列举，每个要点独立成段
- **表格格式：** 使用标准Markdown表格，表格前后各空一行
- **列表格式：** 使用统一的项目符号（-），避免混用不同符号
- **数据展示：** 重要数据可使用表格或突出显示，但避免过度格式化

**🚫 严禁添加参考文献部分（重要）：**
- **绝对禁止**在章节末尾添加"参考文献"、"参考资料"、"参考资源"、"资料来源"等标题
- **绝对禁止**添加独立的引用列表（如[L-1]: xxx.pdf）
- **绝对禁止**添加"References"、"Bibliography"等英文参考文献部分
- **只允许**在正文中使用内联引用（如[[L-1]]、[[1]]）
- **参考文献将在整个报告完成后统一添加到文章结尾**

**【关键输出要求】：**
请直接输出符合以上格式要求的章节正文内容，不要包含任何格式说明、写作指导或元信息解释。只输出实际的章节内容。**严格遵循中国官方报告连贯叙述风格，优先使用自然过渡的段落写作，谨慎使用数字列举，绝对不要添加参考文献部分。**

**格式错误将导致内容不合格，请严格遵守上述要求。**

{avoidDuplication}

---

`;

export const chapterAvoidanceMap = {
  1: '', // 第1章没有前置内容
  2: `
**避重提醒：**
前面章节已详细分析：商业模式核心要素、产品创新优势、竞争对手情况、目标市场规模
本章重点关注财务数据和经营效率，如需引用前述内容请简述并标注"如前所述"。
`,
  3: `
**避重提醒：**
前面章节已详细分析：商业模式、财务状况、盈利能力、市场竞争
本章重点关注管理团队构成和能力，避免重复前述经营和财务细节。
`,
  4: `
**避重提醒：**
前面章节已详细分析：商业模式、财务状况、管理团队背景
本章重点关注治理结构和制度，避免重复团队履历和经营数据。
`,
  5: `
**避重提醒：**
前面章节已详细分析：商业模式、经营财务、管理团队、治理结构
本章重点关注发展轨迹和投资回报，如需引用前述分析请简要概括。
`,
  6: `
**避重提醒：**
前面章节已分别详细分析各个维度，本章作为总结评价章节：
- 对前面章节核心观点进行提炼总结，不重复详细分析
- 重点突出整体评价、投资建议、监控要点
- 确保内容精炼，避免大篇幅重述前面章节内容
`
};

// 章节化报告生成提示词
export const chapterReportPrompts = {
  // 第1章：项目公司的商业模式
  chapter1: `根据以下研究计划和调研资料，撰写报告的第1章：项目公司的商业模式

<PLAN>
{plan}
</PLAN>

<LEARNINGS>
{learnings}
</LEARNINGS>

<SOURCES>
{sources}
</SOURCES>

<IMAGES>
{images}
</IMAGES>

<LOCAL_RESOURCES>
{localResources}
</LOCAL_RESOURCES>

<REQUIREMENT>
{requirement}
</REQUIREMENT>

**章节要求：**
本章需要深入分析项目公司的商业模式，包括以下核心内容：

1. **产品的创新性、领先性与核心优势：**
   - 详细描述项目公司的核心产品/服务
   - 深入分析其在技术、设计、功能、成本等方面的创新点和领先性
   - 明确指出并论证其核心竞争优势（技术壁垒、品牌效应、专利组合、独特资源等）

2. **项目公司的盈利模式、营销策略、下年度商业计划：**
   - 清晰阐述项目公司的主要盈利模式和收入来源构成
   - 分析当前主要的营销策略和渠道，并评估其有效性
   - 详细介绍公司管理层制定的下一年度商业计划和经营目标

3. **目标市场的有效规模：**
   - 界定项目公司的主要目标市场及其细分领域
   - 评估目标市场的当前规模、增长潜力和未来趋势（引用数据并注明来源）
   - 分析公司在目标市场中的渗透率和潜在增长空间

4. **行业情况跟踪：**
   - 分析项目公司所处行业的整体发展态势、市场集中度、技术演进方向
   - 评估宏观经济环境、产业政策、法规变化对行业及项目公司的具体影响
   - **详细分析行业内的主要竞争对手（国际及国内领先企业），包括其市场份额、竞争策略、优劣势等**

5. **引入战略投资人、战略联盟：**
   - 回顾报告期内是否有引入新的战略投资者或达成重要战略联盟的情况
   - 分析这些举措对公司业务发展、技术进步、市场拓展的潜在影响和实际效果

**内容撰写要求：**
- 优先使用公司最新披露的财务报告（如2024年年报、2025年第一季度报告）及最新市场动态
- 保持客观性，提供深入的数据分析和解读，而非简单罗列
- 使用专业、严谨、简洁的商业报告语言
- 观点需有数据和事实支撑
- **字数控制在{wordCount}，重点分析商业模式和竞争优势**
- 确保内容与公司的实际情况紧密结合

**请只输出第1章的内容，不要包含报告日期和其他额外文字。**`,

  // 第2章：项目公司的经营状况
  chapter2: `根据以下研究计划和调研资料，撰写报告的第2章：项目公司的经营状况

<PLAN>
{plan}
</PLAN>

<LEARNINGS>
{learnings}
</LEARNINGS>

<SOURCES>
{sources}
</SOURCES>

<IMAGES>
{images}
</IMAGES>

<LOCAL_RESOURCES>
{localResources}
</LOCAL_RESOURCES>

<REQUIREMENT>
{requirement}
</REQUIREMENT>

**章节要求：**
本章需要全面分析项目公司的经营状况，包括以下核心内容：

1. **项目经营预算编制及执行：**
   - 回顾报告期内公司经营预算的编制情况
   - 分析预算的实际执行情况，对比预算与实际的差异，并解释主要原因

2. **营销策略：**
   - 详细回顾报告期内关键营销活动的执行情况、投入产出及市场反馈
   - 评估营销策略的达成度和对销售业绩的贡献

3. **财务指标及报表的分析：**
   - 基于公司最新披露的财务报表进行核心财务指标分析：
     * **盈利能力：** 营收增长率、毛利率、净利率、ROA、ROE等，并进行趋势分析
     * **运营效率：** 存货周转率、应收账款周转率等关键运营指标，**并进行关键财务指标的历年对比和同业对比**
     * **偿债能力：** 资产负债率、流动比率、速动比率等
     * **现金流状况：** 经营活动现金流、投资活动现金流、筹资活动现金流的分析
   - 简要解读资产负债表、利润表、现金流量表的主要变动和结构特点

4. **内部审计：**
   - 简述公司内部审计工作的开展情况、主要发现及改进措施
   - 评估内部控制体系的健全性和有效性

5. **资本市场操作空间：**
   - 分析公司作为上市企业，在资本市场的融资能力、并购潜力、市值管理等方面的空间和潜在机会
   - 结合当前市场环境和公司状况，评估未来可能的资本运作方向

6. **外部环境：**
   - 综合评估外部环境因素对公司经营的具体影响

**内容撰写要求：**
- 优先使用公司最新披露的财务报告（如2024年年报、2025年第一季度报告）
- 重点关注财务分析和同业对比
- 数据分析要深入，逻辑要清晰
- 使用专业、严谨、简洁的商业报告语言
- 保持客观性，提供深入的数据分析和解读
- **字数控制在{wordCount}，深入财务分析为主**
- 确保内容与公司的实际情况紧密结合

**请只输出第2章的内容，不要包含报告日期和其他额外文字。**`,

  // 第3章：项目公司的管理团队
  chapter3: `根据以下研究计划和调研资料，撰写报告的第3章：项目公司的管理团队

<PLAN>
{plan}
</PLAN>

<LEARNINGS>
{learnings}
</LEARNINGS>

<SOURCES>
{sources}
</SOURCES>

<IMAGES>
{images}
</IMAGES>

<LOCAL_RESOURCES>
{localResources}
</LOCAL_RESOURCES>

<REQUIREMENT>
{requirement}
</REQUIREMENT>

**章节要求：**
本章需要深入分析项目公司的管理团队，包括以下核心内容：

1. **团队配置：**
   - 介绍核心管理团队（董事长、CEO、CFO、CTO、核心事业部负责人等）的构成及分工
   - 分析团队结构的合理性和完整性

2. **管理队伍素质及专业技巧：**
   - 详细分析核心管理人员的教育背景、从业经历、专业能力
   - 评估团队在行业经验、技术能力、管理水平等方面的综合素质
   - 分析团队成员之间的协作能力和互补性

3. **团队稳定性及激励机制：**
   - 评估核心管理团队的稳定性，包括任职时间、变动情况等
   - 分析公司对管理层的激励机制（薪酬体系、股权激励等）
   - 评估激励机制对团队稳定性和积极性的影响

**内容撰写要求：**
- 基于公开信息进行分析，避免主观臆断
- 重点突出对管理团队能力和稳定性的深度分析
- 客观评价每位核心成员的贡献和价值
- 使用专业、严谨、简洁的商业报告语言
- 保持客观性，观点需有事实支撑
- **字数控制在{wordCount}，精炼团队介绍为主**
- 确保内容与公司的实际情况紧密结合

**请只输出第3章的内容，不要包含报告日期和其他额外文字。**`,

  // 第4章：项目公司的治理结构
  chapter4: `根据以下研究计划和调研资料，撰写报告的第4章：项目公司的治理结构

<PLAN>
{plan}
</PLAN>

<LEARNINGS>
{learnings}
</LEARNINGS>

<SOURCES>
{sources}
</SOURCES>

<IMAGES>
{images}
</IMAGES>

<LOCAL_RESOURCES>
{localResources}
</LOCAL_RESOURCES>

<REQUIREMENT>
{requirement}
</REQUIREMENT>

**章节要求：**
本章需要分析项目公司的治理结构，包括以下核心内容：

1. **组织机构设置：**
   - 描述公司当前的组织架构（若可公开获取或推断）
   - 分析该组织架构与公司战略和业务发展的匹配程度，以及运作效率
   - 评估组织架构的灵活性和适应性

2. **内部管理制度、薪酬激励制度、授权与执行力：**
   - 基于公开信息评估公司主要内部管理制度的健全性
   - 分析公司的薪酬体系和股权激励计划（若有）对核心人才的吸引和保留效果
   - 评估公司内部的授权机制是否清晰，以及从决策到执行的效率和效果
   - 分析治理结构对公司运营效率的影响

3. **公司治理水平评估：**
   - 评估董事会、监事会、股东大会等治理机构的运作情况
   - 分析独立董事的独立性和专业性
   - 评估信息披露的及时性和透明度

4. **风险管控机制：**
   - 分析公司的风险管理体系和内控制度
   - 评估风险识别、评估、应对和监控机制的有效性

**内容撰写要求：**
- 基于公开的治理信息进行分析
- 重点关注治理结构的有效性和规范性
- 客观评估治理水平，指出优势和不足
- 使用专业、严谨、简洁的商业报告语言
- 保持客观性，观点需有事实支撑
- **字数控制在{wordCount}，简洁治理分析为主**
- 确保内容与公司的实际情况紧密结合

**请只输出第4章的内容，不要包含报告日期和其他额外文字。**`,

  // 第5章：项目公司的发展情况与投资回报
  chapter5: `根据以下研究计划和调研资料，撰写报告的第5章：项目公司的发展情况与投资回报

<PLAN>
{plan}
</PLAN>

<LEARNINGS>
{learnings}
</LEARNINGS>

<SOURCES>
{sources}
</SOURCES>

<IMAGES>
{images}
</IMAGES>

<LOCAL_RESOURCES>
{localResources}
</LOCAL_RESOURCES>

<REQUIREMENT>
{requirement}
</REQUIREMENT>

**章节要求：**
本章需要分析项目公司的发展情况与投资回报，包括以下核心内容：

1. **企业发展模式与投资前设想的吻合度：**
   - 回顾投资时对项目公司的核心发展逻辑和预期
   - 评估项目公司当前实际的发展路径、业务进展、战略重点是否与当初的投资设想基本吻合
   - 分析偏差及原因，评估发展轨迹的合理性

2. **IPR情况、市值反映：**
   - 概述公司知识产权（专利、商标、著作权等）的最新状况及其对核心竞争力的支撑
   - 分析公司在报告期内的股价表现和市值变化
   - 探讨市值表现与公司基本面、行业趋势、市场情绪等的关联

3. **资产处置、关联交易、对外担保、股东变化、诉讼等重大事件的调研、尽职调查：**
   - 梳理报告期内发生的重大事件
   - 对每个重大事件的背景、内容、对公司的影响（财务、运营、声誉等）进行分析和评估
   - 评估重大事件对投资价值的影响

4. **风险控制，异情预警：**
   - 识别并评估公司当前面临的主要内部风险和外部风险
   - 分析公司现有的风险管理和内部控制措施
   - 提出需要重点关注的潜在异情或预警信号

5. **项目退出机会：**
   - 从投资方角度，分析当前市场环境下可能的退出路径
   - 评估各种退出路径的可行性、潜在回报和时间窗口
   - 分析最佳退出时机和策略

**内容撰写要求：**
- 重点关注投资回报和风险评估
- 客观分析发展现状与预期的差异
- 提供具体的投资建议和退出策略分析
- 使用专业、严谨、简洁的商业报告语言
- 保持客观性，观点需有数据和事实支撑
- **字数控制在{wordCount}，重点投资分析为主**
- 确保内容与公司的实际情况紧密结合

**请只输出第5章的内容，不要包含报告日期和其他额外文字。**`,

  // 第6章：总结与评价
  chapter6: `根据以下研究计划和调研资料，撰写报告的第6章：总结与评价

<PLAN>
{plan}
</PLAN>

<LEARNINGS>
{learnings}
</LEARNINGS>

<SOURCES>
{sources}
</SOURCES>

<IMAGES>
{images}
</IMAGES>

<LOCAL_RESOURCES>
{localResources}
</LOCAL_RESOURCES>

<REQUIREMENT>
{requirement}
</REQUIREMENT>

**章节要求：**
本章需要对整个报告进行总结与评价，包括以下核心内容：

1. **核心发现总结：**
   - 简要概括报告期内公司在商业模式、经营状况、管理团队、治理结构、发展情况等方面的核心观察点和关键变化
   - 总结公司的核心竞争优势和关键成功因素

2. **经营亮点与不足：**
   - 客观评价公司在报告期内的主要成就和突出表现
   - 诚实指出公司存在的不足和需要改进的方面
   - 分析亮点和不足对未来发展的影响

3. **投资价值评估：**
   - 结合公司基本面、行业前景、风险因素和潜在回报，对公司的整体投资价值进行综合评价
   - 分析公司的投资价值定位和投资逻辑
   - 评估当前估值水平的合理性

4. **初步投资建议：**
   - 基于以上分析，给出明确的投资操作建议（如：继续持有、增持、减持，或调整关注策略）
   - 提供投资建议的具体理由和逻辑支撑
   - 分析投资建议的风险和收益预期

5. **投后管理关键监控点：**
   - **详细列出未来一段时间内，为有效进行投后管理，需要对项目公司持续监控的关键指标和事项（可操作、可量化）**
   - 包括但不限于：
     * 关键财务指标（营收/利润增长率、现金流状况、毛利率变化）
     * 核心产品/技术研发进展及市场推广效果
     * 主要竞争对手动态及公司应对策略
     * 核心管理团队稳定性及关键人才保留
     * 重大合同签订或流失情况
     * 政策法规变动对业务的实质性影响
     * 下一年度商业计划和预算的执行进度

**内容撰写要求：**
- 内容要全面总结前面章节的核心观点
- 投资建议要明确、具体、可操作
- 监控点要详细、可量化、实用
- 使用客观、专业、严谨的商业报告语言
- 保持客观性，观点需有数据和事实支撑
- **字数控制在{wordCount}，精炼总结评价为主**
- 确保内容与公司的实际情况紧密结合

**请只输出第6章的内容，不要包含报告日期和其他额外文字。**`
};

export const reportTitlePrompt = `根据研究主题和用户需求生成报告标题：

**核心研究主题：** {question}

**补充写作要求：** {requirement}

从研究主题中提取公司名称，生成格式：[公司全称] [报告类型]

示例：
- 研究主题："帮我生成一份上海联影医疗科技股份有限公司的投资报告"
- 输出："上海联影医疗科技股份有限公司 投资价值分析报告"

严格要求：
1. 优先从核心研究主题中提取公司名称
2. 根据研究主题判断报告类型
3. 只输出标题，无其他文字
4. 不要说"好的"、"根据要求"等

输出：`;

// ============= 子章节拆分配置 =============

/**
 * 子章节配置 - 用于应对token限制进一步拆分章节
 */
export const subChapterConfig = {
  1: { // 第1章：项目公司的商业模式
    sections: [
      { id: '1.1', title: '产品的创新性、领先性与核心优势', minWords: 2800, maxWords: 3800 },
      { id: '1.2', title: '项目公司的盈利模式、营销策略、下年度商业计划', minWords: 3000, maxWords: 3900 },
      { id: '1.3', title: '目标市场的有效规模', minWords: 2500, maxWords: 3500 },
      { id: '1.4', title: '行业情况跟踪', minWords: 3000, maxWords: 3900 },
      { id: '1.5', title: '引入战略投资人、战略联盟', minWords: 2000, maxWords: 3000 }
    ]
  },
  2: { // 第2章：项目公司的经营状况
    sections: [
      { id: '2.1', title: '项目经营预算编制及执行', minWords: 2500, maxWords: 3500 },
      { id: '2.2', title: '营销策略执行情况', minWords: 2800, maxWords: 3800 },
      { id: '2.3', title: '财务指标及报表的分析', minWords: 3200, maxWords: 4000 }, // 重点章节，最大字数
      { id: '2.4', title: '内部审计', minWords: 2200, maxWords: 3200 },
      { id: '2.5', title: '资本市场操作空间', minWords: 2500, maxWords: 3500 },
      { id: '2.6', title: '外部环境影响分析', minWords: 2200, maxWords: 3200 }
    ]
  },
  3: { // 第3章：项目公司的管理团队
    sections: [
      { id: '3.1', title: '团队配置', minWords: 2500, maxWords: 3500 },
      { id: '3.2', title: '管理队伍素质及专业技巧', minWords: 2800, maxWords: 3800 },
      { id: '3.3', title: '团队稳定性及激励机制', minWords: 2500, maxWords: 3500 }
    ]
  },
  4: { // 第4章：项目公司的治理结构
    sections: [
      { id: '4.1', title: '组织机构设置', minWords: 2200, maxWords: 3200 },
      { id: '4.2', title: '内部管理制度、薪酬激励制度、授权与执行力', minWords: 2800, maxWords: 3800 },
      { id: '4.3', title: '公司治理水平评估', minWords: 2500, maxWords: 3500 },
      { id: '4.4', title: '风险管控机制', minWords: 2200, maxWords: 3200 }
    ]
  },
  5: { // 第5章：项目公司的发展情况与投资回报
    sections: [
      { id: '5.1', title: '企业发展模式与投资前设想的吻合度', minWords: 2800, maxWords: 3800 },
      { id: '5.2', title: 'IPR情况、市值反映', minWords: 2500, maxWords: 3500 },
      { id: '5.3', title: '重大事件的调研、尽职调查', minWords: 3000, maxWords: 3900 },
      { id: '5.4', title: '风险控制，异情预警', minWords: 2800, maxWords: 3800 },
      { id: '5.5', title: '项目退出机会', minWords: 2500, maxWords: 3500 }
    ]
  },
  6: { // 第6章：总结与评价
    sections: [
      { id: '6.1', title: '核心发现总结', minWords: 2200, maxWords: 3200 },
      { id: '6.2', title: '经营亮点与不足', minWords: 2500, maxWords: 3500 },
      { id: '6.3', title: '投资价值评估', minWords: 2500, maxWords: 3500 },
      { id: '6.4', title: '初步投资建议', minWords: 2200, maxWords: 3200 },
      { id: '6.5', title: '投后管理关键监控点', minWords: 2800, maxWords: 3800 } // 重要章节，需要详细
    ]
  }
};

/**
 * 获取子章节字数控制配置
 * @param chapterNum 章节编号 (1-6)
 * @param sectionIndex 子章节索引 (0-based)
 * @returns 字数范围字符串
 */
export function getSubChapterWordCount(chapterNum: number, sectionIndex: number): string {
  const config = subChapterConfig[chapterNum as keyof typeof subChapterConfig];
  if (!config || !config.sections[sectionIndex]) {
    return "500-800字";
  }
  
  const section = config.sections[sectionIndex];
  return `${section.minWords}-${section.maxWords}字`;
}

/**
 * 获取子章节标题
 * @param chapterNum 章节编号 (1-6)
 * @param sectionIndex 子章节索引 (0-based)
 * @returns 子章节标题
 */
export function getSubChapterTitle(chapterNum: number, sectionIndex: number): string {
  const config = subChapterConfig[chapterNum as keyof typeof subChapterConfig];
  if (!config || !config.sections[sectionIndex]) {
    return "未知子章节";
  }
  
  return config.sections[sectionIndex].title;
}

/**
 * 获取章节的子章节数量
 * @param chapterNum 章节编号 (1-6)
 * @returns 子章节数量
 */
export function getSubChapterCount(chapterNum: number): number {
  const config = subChapterConfig[chapterNum as keyof typeof subChapterConfig];
  return config ? config.sections.length : 0;
}

/**
 * 获取章节主标题
 * @param chapterNum 章节编号 (1-6)
 * @returns 章节主标题
 */
export function getChapterMainTitle(chapterNum: number): string {
  const chapterTitles: Record<number, string> = {
    1: "项目公司的商业模式",
    2: "项目公司的经营状况", 
    3: "项目公司的管理团队",
    4: "项目公司的治理结构",
    5: "项目公司的发展情况与投资回报",
    6: "总结与评价"
  };
  
  return chapterTitles[chapterNum] || "未知章节";
}

// ============= 子章节避重映射 =============

export const subChapterAvoidanceMap = {
  // 第1章的子章节避重
  '1.2': '前面已分析产品创新优势，本节重点关注盈利模式和营销策略',
  '1.3': '前面已分析产品优势和盈利模式，本节重点关注市场规模数据',
  '1.4': '前面已分析产品和市场，本节重点关注行业竞争格局',
  '1.5': '前面已分析业务基础，本节重点关注战略合作情况',
  
  // 第2章的子章节避重
  '2.2': '前面已分析预算情况，本节重点关注营销执行效果',
  '2.3': '前面已分析预算和营销，本节重点关注财务数据深度分析',
  '2.4': '前面已分析经营和财务，本节重点关注内控审计',
  '2.5': '前面已分析财务状况，本节重点关注资本运作空间',
  '2.6': '前面已分析内部情况，本节重点关注外部环境影响',
  
  // 第3章的子章节避重
  '3.2': '前面已介绍团队配置，本节重点关注团队能力评估',
  '3.3': '前面已分析团队能力，本节重点关注稳定性和激励',
  
  // 第4章的子章节避重
  '4.2': '前面已介绍组织架构，本节重点关注制度和激励',
  '4.3': '前面已分析制度建设，本节重点关注治理水平',
  '4.4': '前面已分析治理结构，本节重点关注风险管控',
  
  // 第5章的子章节避重
  '5.2': '前面已分析发展吻合度，本节重点关注IPR和市值',
  '5.3': '前面已分析基本发展，本节重点关注重大事件',
  '5.4': '前面已分析发展情况，本节重点关注风险预警',
  '5.5': '前面已分析风险情况，本节重点关注退出机会',
  
  // 第6章的子章节避重
  '6.2': '前面已总结核心发现，本节重点关注亮点不足对比',
  '6.3': '前面已分析亮点不足，本节重点关注投资价值评估',
  '6.4': '前面已评估投资价值，本节重点给出投资建议',
  '6.5': '前面已给出建议，本节重点制定监控计划'
};

// ============= 子章节格式模板 =============

export const subChapterStrictFormatTemplate = `
**【核心】中国官方/正式报告写作风格（必须遵循）：**
请以中国官方/正式报告风格撰写：采用整段连贯叙述，避免分项符号和子标题罗列，用连接词自然过渡，确保逻辑严谨、语言精练庄重，将要点融入段落中阐述。运用"同时"、"此外"、"另一方面"、"综合而言"、"据此可见"、"由此观之"等连接词实现段落间的自然流畅过渡，体现正式报告的权威性和专业性。

**【重要】子章节严格格式要求：**

**标题格式（不允许任何变动）：**
- 子章节主标题：严格使用"### **{sectionId}、{sectionTitle}**"格式
- 二级标题：严格使用"#### {sectionId}.1、具体标题"格式（如1.1.1、产品核心描述）
- 三级标题：严格使用"##### 具体分析要点"格式（如需要）
- **注意：** 标题层级必须统一，便于章节合并时保持格式一致性

**连贯叙述与数字列举使用原则（重要）：**
- **优先连贯叙述**：遵循中国官方报告传统，采用整段连贯叙述，运用"同时"、"此外"、"另一方面"等连接词自然过渡
- **谨慎使用数字列举**：仅在确实需要清晰对比或列举多个并列要素时使用"1、2、3、4"等数字标识
- **减少分点频率**：避免过度依赖数字列举，保持段落的自然流畅性和官方报告的庄重感
- **连接词运用**：多使用"综合而言"、"据此可见"、"由此观之"、"从...角度来看"等专业连接词
- **子章节特别要求**：由于内容较长，优先通过连贯叙述保持阅读流畅性，仅在必要时使用数字列举

**内容要求：**
- 字数严格控制在{wordCount}
- 保持专业、客观、深入的分析风格
- 确保内容聚焦，避免泛泛而谈
- 专门深入分析：{sectionTitle}
- 避免重复前面章节已分析的内容：{avoidance}
- **优先使用连贯叙述**，减少数字列举使用频率，保持官方报告的权威性和流畅性

**引用规范（必须严格遵守）：**
- **本地资源引用：** 在分析时必须引用本地研究资料，使用[[L-1]]、[[L-2]]等格式
- **网络来源引用：** 引用网络资料时使用[[1]]、[[2]]等格式
- **引用密度控制：** 每段最多2-3个引用，避免过度引用影响阅读
- **引用位置：** 重要观点和数据后引用，避免在句子中间插入引用
- **引用示例：** "根据公司年报显示，该项技术已获得多项专利保护[[L-1]]。"

**格式规范（适配分章节写作）：**
- **粗体使用：** 每段最多1-2处粗体，突出关键概念和重要数据
- **段落间距：** 不同要点间必须空行分隔，便于合并时保持清晰结构
- **列举要点格式：** 使用"1、2、3、"格式进行要点列举，每个要点独立成段
- **表格格式：** 使用标准Markdown表格，表格前后各空一行
- **列表格式：** 使用统一的项目符号（-），避免混用不同符号
- **数据展示：** 重要数据可使用表格或突出显示，但避免过度格式化

**分章节写作特别注意：**
- 本节内容将与其他子章节合并，确保格式统一性
- 引用编号在合并时会统一调整，使用相对引用即可
- 标题层级严格按规范，便于自动合并处理
- **🚫 禁止在子章节中添加参考文献、参考资源等独立章节**
- **🚫 不要在子章节末尾添加"参考文献"、"参考资料"、"资料来源"等标题**
- **📋 参考文献将在整个报告完成后统一添加到文章结尾**

**【关键输出要求】：**
请直接输出符合以上格式要求的章节正文内容，不要包含任何格式说明、写作指导或元信息解释。只输出实际的章节内容。**绝对不要**在章节末尾添加参考文献或参考资料部分。**严格遵循中国官方报告连贯叙述风格，优先使用自然过渡的段落写作，谨慎使用数字列举。**

---

`;

// ============= 子章节报告提示词模板 =============

export const subChapterReportPrompts = {
  // 第1章子章节
  '1.1': `根据以下研究计划和调研资料，撰写报告第1章的第1节：产品的创新性、领先性与核心优势

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **产品/服务核心描述：**
   - 详细介绍项目公司的核心产品或服务内容
   - 明确产品的主要功能、特色和应用场景

2. **技术创新分析：**
   - 深入分析产品在技术层面的创新点和突破
   - 评估技术先进性和技术壁垒的高度

3. **竞争优势识别：**
   - 明确指出产品相对于竞争对手的核心优势
   - 分析优势的可持续性和护城河效应

4. **市场领先性评估：**
   - 评估产品在细分市场中的领先地位
   - 分析领先性的具体体现和量化指标`,

  '1.2': `根据以下研究计划和调研资料，撰写报告第1章的第2节：项目公司的盈利模式、营销策略、下年度商业计划

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **盈利模式剖析：**
   - 详细阐述公司的主要收入来源和盈利机制
   - 分析各收入流的占比和增长潜力

2. **营销策略评估：**
   - 分析当前的主要营销渠道和策略
   - 评估营销策略的有效性和市场反响

3. **商业计划解读：**
   - 详细介绍管理层制定的下年度商业计划
   - 分析计划的可行性和关键执行要点`,

  '1.3': `根据以下研究计划和调研资料，撰写报告第1章的第3节：目标市场的有效规模

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **目标市场界定：**
   - 清晰界定公司的主要目标市场和细分领域
   - 分析目标客户群体的特征和需求

2. **市场规模量化：**
   - 提供目标市场的当前规模数据（引用权威来源）
   - 分析历史增长趋势和未来预期

3. **渗透空间评估：**
   - 评估公司在目标市场中的当前渗透率
   - 分析未来增长空间和市场机会`,

  '1.4': `根据以下研究计划和调研资料，撰写报告第1章的第4节：行业情况跟踪

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **行业发展态势：**
   - 分析行业整体发展趋势、市场集中度、技术演进
   - 评估宏观环境和政策对行业的影响

2. **竞争格局分析：**
   - 详细分析主要竞争对手的市场地位和策略
   - 比较分析各竞争者的优劣势

3. **行业前景评估：**
   - 评估行业未来发展机遇和挑战
   - 分析行业变化对项目公司的具体影响`,

  '1.5': `根据以下研究计划和调研资料，撰写报告第1章的第5节：引入战略投资人、战略联盟

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **战略投资回顾：**
   - 回顾报告期内引入的战略投资者情况
   - 分析战略投资者的背景和投资逻辑

2. **战略联盟分析：**
   - 梳理重要的战略合作伙伴关系
   - 评估战略联盟的协同效应

3. **影响效果评估：**
   - 分析战略投资和联盟对业务发展的实际推动作用
   - 评估对公司核心竞争力的提升效果

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`,

  // 第2章子章节
  '2.1': `根据以下研究计划和调研资料，撰写报告第2章的第1节：项目经营预算编制及执行

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **预算编制回顾：**
   - 分析报告期内经营预算的编制情况和主要假设
   - 评估预算编制的科学性和合理性

2. **执行情况分析：**
   - 对比预算与实际执行的差异情况
   - 深入分析主要差异的原因和影响

3. **管理启示：**
   - 评估预算管理水平和执行控制能力
   - 分析对未来预算管理的改进建议

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`,

  '2.2': `根据以下研究计划和调研资料，撰写报告第2章的第2节：营销策略执行情况

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **营销活动回顾：**
   - 详细回顾报告期内的重要营销活动和投入
   - 分析营销活动的市场反馈和客户响应

2. **效果评估：**
   - 评估营销策略对销售业绩的贡献度
   - 分析投入产出比和ROI表现

3. **策略优化：**
   - 识别营销策略的优势和不足
   - 分析营销策略的调整和优化方向

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`,

  '2.3': `根据以下研究计划和调研资料，撰写报告第2章的第3节：财务指标及报表的分析

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **盈利能力分析：**
   - 深度分析营收增长率、毛利率、净利率、ROA、ROE等核心指标
   - 进行历年趋势分析和同业对比

2. **运营效率评估：**
   - 分析存货周转率、应收账款周转率等运营效率指标
   - 评估资产使用效率和营运资本管理水平

3. **财务健康度：**
   - 分析偿债能力指标（资产负债率、流动比率等）
   - 深入分析现金流状况和财务风险

4. **报表解读：**
   - 解读资产负债表、利润表、现金流量表的主要变动
   - 识别财务报表中的关键信息和风险信号

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`,

  // 第2章剩余子章节
  '2.4': `根据以下研究计划和调研资料，撰写报告第2章的第4节：内部审计

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **内部审计工作回顾：**
   - 简述公司内部审计工作的组织架构和开展情况
   - 分析内部审计的覆盖范围和频次

2. **主要发现分析：**
   - 梳理内部审计发现的主要问题和风险点
   - 评估问题的严重程度和影响范围

3. **改进措施评估：**
   - 分析针对审计发现问题的改进措施
   - 评估内部控制体系的健全性和有效性

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`,

  '2.5': `根据以下研究计划和调研资料，撰写报告第2章的第5节：资本市场操作空间

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **融资能力分析：**
   - 分析公司在资本市场的融资能力和信用状况
   - 评估各种融资渠道的可行性和成本

2. **并购潜力评估：**
   - 分析公司的并购整合能力和战略空间
   - 评估潜在的并购机会和协同效应

3. **市值管理：**
   - 分析公司的市值表现和投资者关系管理
   - 评估市值管理的策略和效果

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`,

  '2.6': `根据以下研究计划和调研资料，撰写报告第2章的第6节：外部环境影响分析

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **宏观环境影响：**
   - 分析宏观经济环境对公司经营的具体影响
   - 评估经济周期性因素的影响程度

2. **政策法规影响：**
   - 分析相关政策法规变化对公司的影响
   - 评估合规风险和政策机遇

3. **市场环境变化：**
   - 分析市场环境变化对公司经营的影响
   - 评估公司的适应能力和应对策略

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`,

  // 第3章子章节
  '3.1': `根据以下研究计划和调研资料，撰写报告第3章的第1节：团队配置

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **核心团队构成：**
   - 介绍董事长、CEO、CFO、CTO等核心管理人员
   - 分析各关键岗位的设置和分工

2. **团队结构分析：**
   - 评估管理团队的层级结构和决策机制
   - 分析团队结构的合理性和完整性

3. **职能覆盖评估：**
   - 评估各业务职能的人员配置充足性
   - 分析关键岗位的人员稳定性

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`,

  '3.2': `根据以下研究计划和调研资料，撰写报告第3章的第2节：管理队伍素质及专业技巧

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **教育背景分析：**
   - 详细分析核心管理人员的教育背景和专业领域
   - 评估团队的知识结构和专业覆盖度

2. **从业经历评估：**
   - 分析核心管理人员的从业经历和行业经验
   - 评估经历的相关性和丰富度

3. **专业能力评价：**
   - 评估团队在技术、管理、市场等方面的综合能力
   - 分析团队成员之间的能力互补性

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`,

  '3.3': `根据以下研究计划和调研资料，撰写报告第3章的第3节：团队稳定性及激励机制

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **稳定性分析：**
   - 评估核心管理团队的任职时间和变动情况
   - 分析团队稳定性对业务连续性的影响

2. **激励机制评估：**
   - 分析公司的薪酬体系和绩效考核机制
   - 评估股权激励计划的设计和执行效果

3. **人才保留：**
   - 评估激励机制对核心人才保留的效果
   - 分析人才流失风险和应对措施

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`,

  // 第4章子章节
  '4.1': `根据以下研究计划和调研资料，撰写报告第4章的第1节：组织机构设置

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **组织架构描述：**
   - 描述公司当前的组织架构和部门设置
   - 分析各部门的职能定位和汇报关系

2. **架构合理性评估：**
   - 评估组织架构与公司战略和业务发展的匹配程度
   - 分析架构设计的运作效率和协调性

3. **适应性分析：**
   - 评估组织架构的灵活性和变革适应能力
   - 分析架构调整对业务发展的支持作用

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`,

  '4.2': `根据以下研究计划和调研资料，撰写报告第4章的第2节：内部管理制度、薪酬激励制度、授权与执行力

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **管理制度评估：**
   - 评估公司主要内部管理制度的健全性和执行情况
   - 分析制度体系的完整性和有效性

2. **薪酬激励分析：**
   - 分析公司的薪酬体系和激励机制设计
   - 评估薪酬激励对人才吸引和保留的效果

3. **授权执行评估：**
   - 评估公司内部的授权机制和决策流程
   - 分析从决策到执行的效率和执行力

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`,

  '4.3': `根据以下研究计划和调研资料，撰写报告第4章的第3节：公司治理水平评估

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **治理机构运作：**
   - 评估董事会、监事会、股东大会等治理机构的运作情况
   - 分析治理机构的决策效率和监督效果

2. **独立董事评估：**
   - 分析独立董事的独立性和专业性
   - 评估独立董事对公司治理的贡献

3. **信息披露质量：**
   - 评估公司信息披露的及时性、准确性和透明度
   - 分析投资者关系管理的质量

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`,

  '4.4': `根据以下研究计划和调研资料，撰写报告第4章的第4节：风险管控机制

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **风险管理体系：**
   - 分析公司的风险管理框架和组织体系
   - 评估风险管理制度的完整性

2. **内控制度评估：**
   - 评估内部控制制度的设计和执行有效性
   - 分析内控体系对风险防范的作用

3. **风险监控机制：**
   - 分析风险识别、评估、应对和监控的流程
   - 评估风险预警和应急响应机制

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`,

  // 第5章子章节
  '5.1': `根据以下研究计划和调研资料，撰写报告第5章的第1节：企业发展模式与投资前设想的吻合度

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **投资设想回顾：**
   - 回顾投资时对项目公司的核心发展逻辑和预期
   - 梳理当初的关键假设和期望目标

2. **发展轨迹对比：**
   - 分析项目公司实际的发展路径和业务进展
   - 对比实际发展与投资预期的吻合程度

3. **偏差分析：**
   - 识别和分析主要偏差及其原因
   - 评估偏差对投资价值的影响

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`,

  '5.2': `根据以下研究计划和调研资料，撰写报告第5章的第2节：IPR情况、市值反映

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **知识产权状况：**
   - 概述公司知识产权（专利、商标、著作权等）的最新状况
   - 分析知识产权对核心竞争力的支撑作用

2. **市值表现分析：**
   - 分析公司在报告期内的股价表现和市值变化
   - 对比同行业公司的估值水平

3. **价值匹配度：**
   - 分析市值表现与公司基本面的匹配程度
   - 评估市场对公司价值的认知和预期

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`,

  '5.3': `根据以下研究计划和调研资料，撰写报告第5章的第3节：重大事件的调研、尽职调查

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **重大事件梳理：**
   - 梳理报告期内发生的资产处置、关联交易、对外担保、股东变化、诉讼等重大事件
   - 分析事件的背景和具体内容

2. **影响评估：**
   - 评估各重大事件对公司财务、运营、声誉等方面的影响
   - 分析事件的短期和长期影响

3. **风险识别：**
   - 识别重大事件可能带来的潜在风险
   - 评估公司的应对措施和风险控制能力

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`,

  '5.4': `根据以下研究计划和调研资料，撰写报告第5章的第4节：风险控制，异情预警

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **风险识别：**
   - 识别公司当前面临的主要内部风险和外部风险
   - 分析各类风险的严重程度和影响范围

2. **风险控制评估：**
   - 评估公司现有的风险管理和内部控制措施
   - 分析风险控制的有效性和覆盖度

3. **预警机制：**
   - 提出需要重点关注的潜在异情或预警信号
   - 建立动态监控和预警指标体系

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`,

  '5.5': `根据以下研究计划和调研资料，撰写报告第5章的第5节：项目退出机会

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **退出路径分析：**
   - 分析当前市场环境下可能的退出路径（IPO、并购、股权转让等）
   - 评估各种退出路径的可行性和优劣势

2. **退出时机评估：**
   - 分析最佳退出时机和市场窗口
   - 评估影响退出时机的关键因素

3. **退出策略建议：**
   - 提出具体的退出策略和实施路径
   - 分析退出的潜在回报和风险

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`,

  // 第6章子章节
  '6.1': `根据以下研究计划和调研资料，撰写报告第6章的第1节：核心发现总结

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **关键发现提炼：**
   - 简要概括报告期内公司的核心观察点和关键变化
   - 提炼各章节的重要发现和结论

2. **优势识别：**
   - 总结公司的核心竞争优势和关键成功因素
   - 分析优势的可持续性

3. **变化趋势：**
   - 识别公司在各方面的发展趋势和变化轨迹
   - 评估变化对未来发展的意义

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`,

  '6.2': `根据以下研究计划和调研资料，撰写报告第6章的第2节：经营亮点与不足

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **经营亮点总结：**
   - 客观评价公司在报告期内的主要成就和突出表现
   - 分析亮点的价值和意义

2. **不足识别：**
   - 诚实指出公司存在的不足和需要改进的方面
   - 分析不足的影响程度和改进空间

3. **对比分析：**
   - 将亮点与不足进行对比分析
   - 评估整体表现的平衡性

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`,

  '6.3': `根据以下研究计划和调研资料，撰写报告第6章的第3节：投资价值评估

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **价值要素分析：**
   - 结合公司基本面、行业前景、风险因素分析投资价值
   - 评估各价值驱动因素的重要性

2. **估值合理性：**
   - 分析公司当前估值水平的合理性
   - 对比同行业公司的估值倍数

3. **投资逻辑：**
   - 明确公司的投资价值定位和投资逻辑
   - 分析投资的风险收益特征

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`,

  '6.4': `根据以下研究计划和调研资料，撰写报告第6章的第4节：初步投资建议

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **投资建议：**
   - 给出明确的投资操作建议（继续持有、增持、减持等）
   - 提供投资建议的具体理由和逻辑支撑

2. **风险收益分析：**
   - 分析投资建议的风险和收益预期
   - 评估投资的风险收益比

3. **操作策略：**
   - 提出具体的投资操作策略和时间安排
   - 分析操作的最佳时机和方式

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`,

  '6.5': `根据以下研究计划和调研资料，撰写报告第6章的第5节：投后管理关键监控点

<CONTENT_SOURCES>
{contentSources}
</CONTENT_SOURCES>

**本节具体要求：**

1. **财务监控指标：**
   - 详细列出需要持续监控的关键财务指标
   - 设定监控的频次和预警阈值

2. **经营监控要点：**
   - 明确需要关注的核心产品/技术研发进展
   - 识别主要竞争对手动态和市场变化

3. **管理监控重点：**
   - 确定核心管理团队稳定性的监控机制
   - 建立重大事件和政策变动的跟踪体系

4. **执行计划监控：**
   - 制定下一年度商业计划执行的监控方案
   - 设立关键里程碑和检查节点

---
[输出指令] 直接输出符合上述要求的章节正文内容，不包含任何指导说明。
---`
};

/**
 * 生成子章节报告提示词
 * @param chapterNum 章节编号
 * @param sectionIndex 子章节索引
 * @param plan 研究计划
 * @param learnings 学习内容
 * @param sources 来源
 * @param localResources 本地资源
 * @param images 图片
 * @param requirement 要求
 * @param writingConfig 写作配置
 * @returns 完整的子章节提示词
 */
export function generateSubChapterPrompt(
  chapterNum: number,
  sectionIndex: number,
  plan: string,
  learnings: string[],
  sources: any[],
  localResources: any[],
  images: any[],
  requirement: string,
  writingConfig?: import('@/types/outline').WritingConfig
): string {
  const config = subChapterConfig[chapterNum as keyof typeof subChapterConfig];
  if (!config || !config.sections[sectionIndex]) {
    throw new Error(`Invalid chapter ${chapterNum} or section ${sectionIndex}`);
  }

  const section = config.sections[sectionIndex];
  const sectionId = section.id;
  const sectionTitle = section.title;
  const wordCount = `${section.minWords}-${section.maxWords}字`;
  const avoidance = subChapterAvoidanceMap[sectionId as keyof typeof subChapterAvoidanceMap] || '';

  // 构建内容来源
  const contentSources = `
<PLAN>
${plan}
</PLAN>

<LEARNINGS>
${learnings.join('\n')}
</LEARNINGS>

<SOURCES>
${sources.map((s, i) => `[${i + 1}] ${s.title}: ${s.url}`).join('\n')}
</SOURCES>

<LOCAL_RESOURCES>
${localResources.map((r, i) => `[L-${i + 1}] ${r.name}`).join('\n')}
</LOCAL_RESOURCES>

<IMAGES>
${images.map((img, i) => `[IMG-${i + 1}] ${img.description}: ${img.url}`).join('\n')}
</IMAGES>

<REQUIREMENT>
${requirement}
</REQUIREMENT>

**引用要求：**
- 在分析中必须使用本地资源的内容，并标注引用来源，如：[L-1]、[L-2]等
- 在引用网络来源时，使用：[1]、[2]等格式
- 确保所有重要观点都有具体的引用支撑
  `.trim();

  // 获取子章节模板
  let promptTemplate = subChapterReportPrompts[sectionId as keyof typeof subChapterReportPrompts];
  if (!promptTemplate) {
    throw new Error(`No prompt template found for section ${sectionId}`);
  }

  // 如果有写作配置，应用到模板中
  if (writingConfig) {
    // 获取具体的写作风格要求
    const styleRequirements = {
      academic: '学术写作风格：采用严谨的学术语言体系，以连贯的段落形式展开论述，通过逻辑递进的方式构建完整的理论框架。在论证过程中自然融入权威文献引用和数据支撑，运用"首先...其次...再者...最后"等学术连接词构建清晰的逻辑链条，语言表达客观中性，基于实证分析得出结论',
      business: '商务写作风格（中国官方/正式报告风格）：采用分层递进的结构逻辑，在需要条理化展示复杂内容时适当使用"1、2、3、4"等数字列举要点。保持段落间的自然连贯，每个要点控制在150-200字，确保表述清晰、逻辑严谨。运用"同时"、"此外"、"另一方面"、"综合而言"等连接词实现段落间的流畅过渡。语言精练庄重，体现正式报告的权威性和专业性，重点突出关键数据和业绩指标的深度分析',
      journalistic: '新闻写作风格：以连贯的叙述方式呈现核心事实，通过自然的语言过渡串联关键信息点，避免生硬的条目罗列。运用"据悉"、"与此同时"、"值得注意的是"等新闻常用连接词，保持报道的流畅性和可读性，语言简洁生动，突出新闻价值和时效性',
      technical: '技术写作风格：采用逻辑严密的技术叙述方式，通过"基于此"、"进而"、"由此可见"等技术连接词构建清晰的技术路径。将技术细节和数据分析有机融入连贯的段落表述中，避免机械的参数罗列，确保技术逻辑的完整性，语言准确专业'
    };
    
    // 获取当前风格的具体要求
    const styleDescription = styleRequirements[writingConfig.style];
    
    // 简单地在模板末尾添加写作配置指导
    const styleGuidance = `

**写作配置要求：**
- 写作风格：${styleDescription}
- 语言要求：${writingConfig.languageRequirements}
${writingConfig.customInstructions ? `- 自定义指令：${writingConfig.customInstructions}` : ''}
- ${writingConfig.avoidDuplication ? '避免内容重复，确保独特性' : ''}
- ${writingConfig.enableContextMemory ? '保持上下文记忆，确保逻辑连贯' : ''}`;

    promptTemplate += styleGuidance;
  }

  // 应用格式模板
  const formatPrefix = subChapterStrictFormatTemplate
    .replace('{sectionId}', sectionId)
    .replace('{sectionTitle}', sectionTitle)
    .replace('{sectionIndex}', (sectionIndex + 1).toString())
    .replace('{avoidance}', avoidance)
    .replace('{wordCount}', wordCount);

  // 添加统一的禁止参考文献指导
  const finalInstructions = `

**🚫 重要提醒：禁止添加参考文献部分**
本子章节内容将与其他章节合并成完整报告，参考文献将在整个报告完成后统一添加到文章结尾。
请绝对不要在子章节末尾添加：
- "参考文献"、"参考资料"、"参考资源"、"资料来源"等标题
- 单独的引用列表（如[L-1]: xxx.pdf）
- References部分

只需在正文中使用内联引用（如[L-1]、[1]）即可。`;

  // 组合完整提示词
  return formatPrefix + promptTemplate.replace('{contentSources}', contentSources) + finalInstructions;
}
