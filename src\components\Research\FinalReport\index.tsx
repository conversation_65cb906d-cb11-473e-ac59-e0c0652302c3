"use client";
import dynamic from "next/dynamic";
import { useState, useMemo, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Download,
  FileText,
  FileType,
  Signature,
  LoaderCircle,
  NotebookText,
  Waypoints,
} from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { Button } from "@/components/Internal/Button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import useAccurateTimer from "@/hooks/useAccurateTimer";

import { useOutlineDrivenResearch } from "@/hooks/useOutlineDrivenResearch";
import useKnowledge from "@/hooks/useKnowledge";
import { useTaskStore } from "@/store/task";
import { useKnowledgeStore } from "@/store/knowledge";
import { useOutlineStore } from "@/store/outline";
import { getSystemPrompt } from "@/utils/deep-research/prompts";
import { downloadFile } from "@/utils/file";
import { exportToWord } from "@/utils/wordExporter";
import { ChapterInfo } from "@/components/MagicDown";

const MagicDown = dynamic(() => import("@/components/MagicDown"));
const Artifact = dynamic(() => import("@/components/Artifact"));
const KnowledgeGraph = dynamic(() => import("./KnowledgeGraph"));

const formSchema = z.object({
  requirement: z.string().optional(),
});

// type ReportGenerationMode = "outline_driven";

function FinalReport() {
  const { t } = useTranslation();
  const taskStore = useTaskStore();
  const { currentOutline, generateChapterMap } = useOutlineStore();

  const { 
    writeOutlineDrivenReport, 
    status 
  } = useOutlineDrivenResearch();
  const { generateId } = useKnowledge();
  const {
    formattedTime,
    start: accurateTimerStart,
    stop: accurateTimerStop,
  } = useAccurateTimer();
  const [isWriting, setIsWriting] = useState<boolean>(false);
  const [openKnowledgeGraph, setOpenKnowledgeGraph] = useState<boolean>(false);
  // const [reportMode, setReportMode] = useState<ReportGenerationMode>("outline_driven");
  
  // 🔥 生成章节映射 - 依赖大纲和报告内容
  const chapterMap = useMemo(() => {
    if (currentOutline) {
      console.log("🗺️ 重新生成章节映射，报告长度:", taskStore.finalReport.length);
      const map = generateChapterMap(currentOutline.id);
      console.log("🗺️ 章节映射生成完成，共", map?.size || 0, "个条目");
      return map;
    }
    return null;
  }, [currentOutline, generateChapterMap, taskStore.finalReport]); // 🔥 添加finalReport依赖
  
  const taskFinished = useMemo(() => {
    const unfinishedTasks = taskStore.tasks.filter(
      (task) => task.state !== "completed"
    );
    return taskStore.tasks.length > 0 && unfinishedTasks.length === 0;
  }, [taskStore.tasks]);
  
  // 处理章节编辑
  const handleChapterEdit = (chapterInfo: ChapterInfo) => {
    console.log("📝 FinalReport收到章节编辑请求:", chapterInfo);
    
    // 这里可以添加打开章节编辑对话框的逻辑
    // 或者触发其他章节编辑相关的操作
    
    // 比如：设置当前编辑的章节到task store
    // taskStore.startChapterEdit(chapterInfo);
  };

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      requirement: "", // 🔥 改为空，让 useEffect 来设置，避免覆盖缓存数据
    },
  });

  async function handleSubmit(values: z.infer<typeof formSchema>) {
    const { setRequirement } = useTaskStore.getState();
    try {
      accurateTimerStart();
      setIsWriting(true);
      
      // 🔥 处理写作要求，确保包含研究主题
      if (values.requirement) {
        const requirementText = values.requirement.startsWith("研究主题：") && values.requirement === `研究主题：${taskStore.question}`
          ? `${values.requirement}\n\n请撰写详细的投资价值分析报告，包含商业模式、经营状况、管理团队、治理结构、发展情况和总结评价等核心内容。`
          : values.requirement;
        setRequirement(requirementText);
      }
      
      await writeOutlineDrivenReport();
    } finally {
      setIsWriting(false);
      accurateTimerStop();
    }
  }

  function getFinakReportContent() {
    const { finalReport, resources, sources } = useTaskStore.getState();

    return [
      finalReport,
      resources.length > 0
        ? [
            "---",
            `## ${t("research.finalReport.localResearchedInfor", {
              total: resources.length,
            })}`,
            `${resources
              .map((source, idx) => `${idx + 1}. ${source.name}`)
              .join("\n")}`,
          ].join("\n")
        : "",
      sources.length > 0
        ? [
            "---",
            `## ${t("research.finalReport.researchedInfor", {
              total: sources.length,
            })}`,
            `${sources
              .map(
                (source, idx) =>
                  `${idx + 1}. [${source.title || source.url}][${idx + 1}]`
              )
              .join("\n")}`,
          ].join("\n")
        : "",
    ].join("\n\n");
  }

  function addToKnowledgeBase() {
    const { title } = useTaskStore.getState();
    const { save } = useKnowledgeStore.getState();
    const currentTime = Date.now();
    save({
      id: generateId("knowledge"),
      title,
      content: getFinakReportContent(),
      type: "knowledge",
      createdAt: currentTime,
      updatedAt: currentTime,
    });
    toast.message(t("research.common.addToKnowledgeBaseTip"));
  }

  async function handleDownloadPDF() {
    const originalTitle = document.title;
    document.title = taskStore.title;
    window.print();
    document.title = originalTitle;
  }

  async function handleDownloadWord() {
    try {
      await exportToWord(
        taskStore.title,
        taskStore.finalReport,
        taskStore.resources,
        taskStore.sources
      );
      toast.success(t("exportSuccess"));
    } catch (error) {
      console.error("Word导出失败:", error);
      toast.error(t("exportFailed"));
    }
  }

  useEffect(() => {
    // 🔥 确保写作要求字段包含研究主题信息
    const requirementValue = taskStore.requirement || `研究主题：${taskStore.question}`;
    form.setValue("requirement", requirementValue);
  }, [taskStore.requirement, taskStore.question, form]);

  return (
    <>
      <section className="p-4 border rounded-md mt-4 print:border-none">
        <h3 className="font-semibold text-lg border-b mb-2 leading-10 print:hidden">
          {t("research.finalReport.title")}
        </h3>
        {taskStore.finalReport !== "" ? (
          <article className="max-w-none prose prose-slate dark:prose-invert">
            <MagicDown
              className="min-h-72"
              value={taskStore.finalReport}
              onChange={(value) => taskStore.updateFinalReport(value)}
              enableChapterEdit={true}
              chapterMap={chapterMap || undefined}
              onChapterEdit={handleChapterEdit}
              tools={
                <>
                  <div className="px-1">
                    <Separator className="dark:bg-slate-700" />
                  </div>
                  <Artifact
                    value={taskStore.finalReport}
                    systemInstruction={getSystemPrompt()}
                    onChange={taskStore.updateFinalReport}
                    buttonClassName="float-menu-button"
                    dropdownMenuSideOffset={8}
                    tooltipSideOffset={8}
                  />
                  <div className="px-1">
                    <Separator className="dark:bg-slate-700" />
                  </div>
                  {false && (
                    <Button
                      className="float-menu-button"
                      type="button"
                      size="icon"
                      variant="ghost"
                      title={t("knowledgeGraph.action")}
                      side="left"
                      sideoffset={8}
                      onClick={() => setOpenKnowledgeGraph(true)}
                    >
                      <Waypoints />
                    </Button>
                  )}
                  {false && (
                    <Button
                      className="float-menu-button"
                      type="button"
                      size="icon"
                      variant="ghost"
                      title={t("research.common.addToKnowledgeBase")}
                      side="left"
                      sideoffset={8}
                      onClick={() => addToKnowledgeBase()}
                    >
                      <NotebookText />
                    </Button>
                  )}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        className="float-menu-button"
                        type="button"
                        size="icon"
                        variant="ghost"
                        title={t("research.common.export")}
                        side="left"
                        sideoffset={8}
                      >
                        <Download />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      className="print:hidden"
                      side="left"
                      sideOffset={8}
                    >
                      <DropdownMenuItem
                        onClick={() =>
                          downloadFile(
                            getFinakReportContent(),
                            `${taskStore.title}.md`,
                            "text/markdown;charset=utf-8"
                          )
                        }
                      >
                        <FileText />
                        <span>Markdown</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="max-md:hidden"
                        onClick={() => handleDownloadPDF()}
                      >
                        <Signature />
                        <span>PDF</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDownloadWord()}
                      >
                        <FileType />
                        <span>Word</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </>
              }
            />
            {taskStore.resources.length > 0 ? (
              <div className="prose prose-slate dark:prose-invert">
                <hr className="my-6" />
                <h2>
                  {t("research.finalReport.localResearchedInfor", {
                    total: taskStore.resources.length,
                  })}
                </h2>
                <ul>
                  {taskStore.resources.map((resource) => {
                    return <li key={resource.id}>{resource.name}</li>;
                  })}
                </ul>
              </div>
            ) : null}
            {taskStore.sources?.length > 0 ? (
              <div className="prose prose-slate dark:prose-invert">
                <hr className="my-6" />
                <h2>
                  {t("research.finalReport.researchedInfor", {
                    total: taskStore.sources.length,
                  })}
                </h2>
                <ol>
                  {taskStore.sources.map((source, idx) => {
                    return (
                      <li key={idx}>
                        <a href={source.url} target="_blank">
                          {source.title || source.url}
                        </a>
                      </li>
                    );
                  })}
                </ol>
              </div>
            ) : null}
          </article>
        ) : null}
        {taskFinished ? (
          <Form {...form}>
            <form
              className="mt-4 border-t pt-4 print:hidden"
              onSubmit={form.handleSubmit(handleSubmit)}
            >
              <FormField
                control={form.control}
                name="requirement"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="mb-2 font-semibold">
                      {t("research.finalReport.writingRequirementLabel")}
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        rows={3}
                        placeholder={t(
                          "research.finalReport.writingRequirementPlaceholder"
                        )}
                        disabled={isWriting}
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <div className="mt-4">
                <Button
                  className="w-full"
                  type="submit"
                  disabled={isWriting || !taskStore.isOutlineDriven}
                >
                  {isWriting ? (
                    <>
                      <LoaderCircle className="animate-spin" />
                      <span>{status}</span>
                      <small className="font-mono">{formattedTime}</small>
                    </>
                  ) : taskStore.finalReport === "" ? (
                    t("research.common.writeReport")
                  ) : (
                    t("research.common.rewriteReport")
                  )}
                </Button>
              </div>
              
              {/* 大纲驱动方式说明 */}
              <div className="mt-2 text-sm text-gray-600">
                {taskStore.isOutlineDriven ? (
                  <div className="p-2 bg-green-50 dark:bg-green-950/20 rounded border-l-4 border-green-400">
                    <p className="flex items-center gap-1 font-medium">
                      <FileText className="w-4 h-4" />
                      大纲驱动生成模式
                    </p>
                    <p className="text-xs mt-1">
                      📋 基于预设大纲结构，按章节顺序生成高质量报告，确保内容完整性和逻辑性
                    </p>
                  </div>
                ) : (
                  <div className="p-2 bg-yellow-50 dark:bg-yellow-950/20 rounded border-l-4 border-yellow-400">
                    <p className="flex items-center gap-1 font-medium">
                      <FileText className="w-4 h-4" />
                      需要启用大纲驱动模式
                    </p>
                    <p className="text-xs mt-1">
                      ⚠️ 请先在研究反馈面板中启用大纲驱动模式，才能使用基于大纲的报告生成功能
                    </p>
                  </div>
                )}
              </div>
            </form>
          </Form>
        ) : null}
        {taskStore.finalReport === "" && !taskFinished ? (
          <div>{t("research.finalReport.emptyTip")}</div>
        ) : null}
      </section>
      {openKnowledgeGraph ? (
        <KnowledgeGraph
          open={openKnowledgeGraph}
          onClose={() => setOpenKnowledgeGraph(false)}
        ></KnowledgeGraph>
      ) : null}
    </>
  );
}

export default FinalReport;
