{"title": "AI驱动的企业管理报告自动化平台", "theme": "系统主题", "openSource": "开源代码", "copyright": "上海聚荐科技构建", "searchPlaceholder": "请输入关键词", "research": {"common": {"startThinking": "开始思考", "rethinking": "重新思考", "thinkingQuestion": "思考问题...", "writeReportPlan": "撰写报告方案", "rewriteReportPlan": "重写报告方案", "startResearch": "开始研究", "restartResearch": "重新研究", "writeReport": "撰写报告", "continueResearch": "继续研究", "indepthResearch": "进一步研究", "rewriteReport": "重写报告", "writeChapterReport": "按章节撰写报告", "rewriteChapterReport": "重新按章节撰写报告", "sources": "资料来源", "thinking": "思考中...", "research": "研究中...", "writing": "正在撰写报告...", "writingTitle": "正在生成报告标题...", "writingChapter": "正在撰写章节", "newResearch": "开始新研究", "addToKnowledgeBase": "添加到知识库", "addToKnowledgeBaseTip": "已添加到知识库", "restudy": "重新研究", "edit": "编辑", "save": "保存", "copy": "复制", "export": "导出", "delete": "删除"}, "topic": {"title": "1. 研究方向", "topicLabel": "1.1 研究主题", "topicPlaceholder": "任何您想了解的问题..."}, "feedback": {"title": "2. 提出您的想法", "emptyTip": "等待确定研究主题...", "feedbackLabel": "您的回答（可选）", "feedbackPlaceholder": "畅所欲言，谈谈您的见解...", "questions": "2.1 系统提问", "reportPlan": "2.2 研究报告方案"}, "searchResult": {"title": "3. 预写", "emptyTip": "等待分配研究任务...", "suggestionLabel": "研究建议（可选）", "suggestionPlaceholder": "是否增加或者调整研究方向...", "references": "参考文献", "relatedImages": "相关图片"}, "finalReport": {"title": "4. 最终报告", "emptyTip": "等待数据汇总...", "researchedInfor": "已研究 {{total}} 个网站", "localResearchedInfor": "已研究 {{total}} 个本地资源", "writingRequirementLabel": "写作要求（已包含研究主题）", "writingRequirementPlaceholder": "默认已包含研究主题。您可以在此基础上添加其他写作要求，如格式要求、重点分析方向等。"}}, "history": {"title": "历史记录", "description": "研究历史保存在浏览器本地，仅保存已完成的研究。", "name": "标题", "emptyTip": "没有历史记录", "date": "日期", "actions": "操作", "import": "导入", "importTip": "导入研究", "importSuccess": "{{title}} 导入成功。", "importFailed": "{{title}} 导入失败。", "load": "加载", "export": "导出", "delete": "删除", "loadMore": "加载更多历史", "close": "关闭", "noHistory": "没有历史记录"}, "knowledge": {"title": "本地知识库", "description": "存储在浏览器本地的知识库。", "create": "创建", "createTip": "创建新知识", "clearAll": "全部清空", "clearAllTip": "清空所有知识库条目", "clearAllConfirm": "确定要清空所有知识库吗？此操作不可撤销。", "clearAllSuccess": "已清空所有知识库", "emptyTip": "没有内容", "name": "标题", "size": "大小", "date": "日期", "action": "操作", "add": "添加", "edit": "编辑", "delete": "删除", "loadMore": "加载更多知识", "resource": "资源", "fileInfor": "由用户于 {{createdAt}} 上传。", "urlInfor": "由用户于 {{createdAt}} 获取。", "createInfor": "由用户于 {{createdAt}} 创建。", "webCrawler": "网页爬取器", "webCrawlerTip": "网页爬取器通过服务器获取指定 URL 的页面内容, 并以 Markdown 格式返回数据。", "urlPlaceholder": "请输入网址...", "urlError": "请输入有效的 URL", "localCrawler": "本地爬取器", "clear": "清除", "fetch": "获取", "localResourceTitle": "1.2 本地研究资源 (可选)", "addResource": "添加资源", "addResourceMessage": "{{title}} 已添加到资源。", "resourceNotFound": "未找到资源", "knowledge": "知识", "localFile": "本地文件", "webPage": "网页", "editor": {"title": "标题", "titlePlaceholder": "请输入标题...", "content": "内容 (Markdown)", "back": "返回", "reset": "重置", "submit": "提交"}}, "outline": {"title": "大纲管理", "description": "管理您的研究大纲，支持创建、编辑、导入导出等功能。", "create": "创建大纲", "edit": "编辑大纲", "delete": "删除大纲", "duplicate": "复制大纲", "export": "导出大纲", "import": "导入大纲", "apply": "应用大纲", "templates": "模板管理", "settings": "设置", "search": "搜索大纲", "chapters": "章节", "emptyTip": "还没有大纲", "createFirst": "创建您的第一个研究大纲", "template": {"edit": "编辑模板", "delete": "删除模板", "duplicate": "复制模板", "apply": "应用模板", "builtin": "内置模板", "cannotDelete": "内置模板无法删除", "deleteConfirm": "确定要删除模板 \"{name}\" 吗？此操作不可撤销。", "updated": "模板 \"{name}\" 已更新", "duplicated": "模板 \"{name}\" 已复制"}}, "artifact": {"AIWrite": "AI 写作", "writingPromptTip": "请输入写作需求...", "adjustLength": "调整长度", "longest": "更长", "long": "长", "shorter": "短", "shortest": "更短", "translate": "翻译", "continuation": "续写", "addEmojis": "添加表情", "send": "发送"}, "knowledgeGraph": {"action": "生成知识图谱", "regenerate": "重新生成", "edit": "编辑", "view": "查看"}, "editor": {"copy": "复制", "mermaid": {"downloadSvg": "下载为图片", "copyText": "复制文本", "zoomIn": "放大", "zoomOut": "缩小", "resize": "重置大小"}, "tooltip": {"bold": "粗体", "italic": "斜体", "strikethrough": "删除线", "code": "代码", "math": "数学公式", "link": "链接", "quote": "引用"}, "slash": {"heading": {"name": "标题", "description": "插入标题"}, "h1": {"name": "一级标题", "description": "插入一级标题"}, "h2": {"name": "二级标题", "description": "插入二级标题"}, "h3": {"name": "三级标题", "description": "插入三级标题"}, "h4": {"name": "四级标题", "description": "插入四级标题"}, "h5": {"name": "五级标题", "description": "插入五级标题"}, "h6": {"name": "六级标题", "description": "插入六级标题"}, "list": {"name": "列表", "description": "插入列表"}, "ul": {"name": "无序列表", "description": "插入无序列表项"}, "ol": {"name": "有序列表", "description": "插入有序列表项"}, "todo": {"name": "待办事项", "description": "插入待办事项"}, "advanced": {"name": "高级", "description": "高级命令"}, "link": {"name": "链接", "description": "插入链接"}, "image": {"name": "图片", "description": "插入图片"}, "code": {"name": "代码块", "description": "插入代码块"}, "math": {"name": "数学公式", "description": "插入数学公式"}, "table": {"name": "表格", "description": "插入一个 3x3 表格"}, "quote": {"name": "引用", "description": "插入引用"}, "horizontal": {"name": "水平线", "description": "插入水平线"}}, "placeholder": "请输入文本，或输入 \"/ \" 使用指令"}, "setting": {"title": "设置", "description": "所有设置都将保存在您的浏览器中。", "model": "语言模型", "general": "通用", "provider": "AI 服务", "providerTip": "AI 服务提供商。对于 One API 和 New API 这类 AI 聚合服务，请选择 `兼容 OpenAI`。", "openAICompatible": "OpenAI 兼容", "free": "免费", "mode": "API 模式", "modeTip": "本地模式下，所有请求都直接由浏览器发送。代理模式下，所有请求会通过服务器（代理）转发。", "local": "本地", "proxy": "代理", "apiKeyLabel": "API 密钥", "apiKeyPlaceholder": "请输入模型 API 密钥", "apiUrlLabel": "API 基础 URL", "apiUrlPlaceholder": "请输入 API 基础 URL", "resourceNameLabel": "资源名称", "resourceNamePlaceholder": "请输入资源名称", "apiVersionLabel": "API 版本", "apiVersionPlaceholder": "请输入 API 版本", "accessPassword": "访问密码", "accessPasswordPlaceholder": "请输入服务器访问密码", "accessPasswordTip": "服务端 API 鉴权，避免 API 被外部应用盗用。", "thinkingModel": "思考模型", "thinkingModelTip": "深度研究使用的核心模型，建议选择擅长思考的模型。", "networkingModel": "联网模型", "networkingModelTip": "负责辅助任务的模型，建议选择输出效率高的模型。", "recommendedModels": "推荐模型", "basicModels": "基础模型", "modelListLoadingPlaceholder": "请选择模型", "modelListPlaceholder": "请输入模型名称", "refresh": "点击刷新模型", "modelListLoading": "模型列表加载中", "search": "搜索参数", "webSearch": "联网搜索", "webSearchTip": "开启联网搜索功能可以获取最新数据，有利于减少大语言模型的\"幻觉\"。强烈推荐启用。", "enable": "启用", "disable": "禁用", "searchProvider": "搜索供应商", "searchProviderTip": "搜索服务提供商。部分模型内置了联网搜索能力，但大多数模型需要借助第三方搜索引擎才能实现联网查询。", "modelBuiltin": "模型内置", "bocha": "博查", "parallelSearch": "并行搜索数量", "parallelSearchTip": "同时运行的搜索引擎数量，建议设置为1以节省API费用", "searchResults": "搜索数量", "searchResultsTip": "最大搜索数量。部分搜索引擎并不支持此参数。", "searchApiKeyPlaceholder": "请输入您的 API 密钥", "searchScope": "搜索范围", "scopeValue": {"all": "全部", "academic": "学术", "general": "常规", "news": "新闻", "researchPaper": "研究论文", "financial": "金融", "company": "公司", "personalSite": "个人网站", "github": "<PERSON><PERSON><PERSON>", "linkedin": "领英", "pdf": "PDF"}, "language": "语言", "languageTip": "系统会根据用户浏览器语言自动选择界面语言。", "system": "跟随系统", "light": "明亮", "dark": "黑暗", "debug": "调试模式", "debugTip": "开启调试模式可以在控制台查看详细日志", "PWA": "安装PWA", "PWATip": "渐进式 Web 应用是一个使用 web 平台技术构建的应用程序，它提供与原生应用程序相近的用户体验。", "installlPWA": "安装浏览器应用(PWA)", "resetSetting": "重置设置", "resetAllSettings": "重置设置并清空缓存", "resetSettingWarning": "此操作将清空所有数据并初始化项目", "version": "当前版本", "checkForUpdate": "检查更新", "experimental": "实验性功能", "references": "引用", "referencesTip": "启用引用功能可以在报告中显示信息来源", "citationImage": "引用图片", "citationImageTip": "启用引用图片功能可以在报告中显示引用的图片", "save": "保存", "confirm": "确认", "cancel": "取消", "apiKey": "API密钥", "apiProxy": "API代理", "openRouterApiKey": "OpenRouter API密钥", "openRouterApiProxy": "OpenRouter API代理", "openRouterThinkingModel": "OpenRouter思考模型", "openRouterNetworkingModel": "OpenRouter联网模型", "openAIApiKey": "OpenAI API密钥", "openAIApiProxy": "OpenAI API代理", "openAIThinkingModel": "OpenAI思考模型", "openAINetworkingModel": "OpenAI联网模型", "anthropicApiKey": "Anthropic API密钥", "anthropicApiProxy": "Anthropic API代理", "anthropicThinkingModel": "Anthropic思考模型", "anthropicNetworkingModel": "Anthropic联网模型", "deepseekApiKey": "DeepSeek API密钥", "deepseekApiProxy": "DeepSeek API代理", "deepseekThinkingModel": "DeepSeek思考模型", "deepseekNetworkingModel": "DeepSeek联网模型", "xAIApiKey": "xAI API密钥", "xAIApiProxy": "xAI API代理", "xAIThinkingModel": "xAI思考模型", "xAINetworkingModel": "xAI联网模型", "mistralApiKey": "Mistral API密钥", "mistralApiProxy": "Mistral API代理", "mistralThinkingModel": "Mistral思考模型", "mistralNetworkingModel": "Mistral联网模型", "azureApiKey": "Azure API密钥", "azureResourceName": "Azure资源名称", "azureApiVersion": "Azure API版本", "azureThinkingModel": "Azure思考模型", "azureNetworkingModel": "Azure联网模型", "openAICompatibleApiKey": "兼容API密钥", "openAICompatibleApiProxy": "兼容API代理", "openAICompatibleThinkingModel": "兼容思考模型", "openAICompatibleNetworkingModel": "兼容联网模型", "pollinationsApiProxy": "Pollinations API代理", "pollinationsThinkingModel": "Pollinations思考模型", "pollinationsNetworkingModel": "Pollinations联网模型", "ollamaApiProxy": "Ollama API代理", "ollamaThinkingModel": "Ollama思考模型", "ollamaNetworkingModel": "Ollama联网模型", "selectProvider": "选择供应商", "selectSearchProvider": "选择搜索供应商", "searchMaxResult": "搜索最大结果数", "searchMaxResultTip": "每个搜索引擎返回的最大结果数量，建议设置为5", "tavilyApiKey": "Tavily API密钥", "tavilyApiProxy": "Tavily API代理", "tavilyScope": "Tavily搜索范围", "firecrawlApiKey": "Firecrawl API密钥", "firecrawlApiProxy": "Firecrawl API代理", "exaApiKey": "Exa API密钥", "exaApiProxy": "Exa API代理", "exaScope": "Exa搜索范围", "bochaApiKey": "Bocha API密钥", "bochaApiProxy": "Bocha API代理", "searxngApiProxy": "SearXNG API代理", "searxngScope": "SearXNG搜索范围", "academic": "学术", "business": "商业", "research": "研究", "finance": "金融", "news": "新闻", "health": "健康", "government": "政府", "researchPaper": "研究论文", "all": "全部", "crawler": "爬虫", "selectCrawler": "选择爬虫", "jinaReader": "<PERSON><PERSON>", "rScraper": "<PERSON>", "firecrawl": "Firecrawl", "enableSearch": "启用搜索", "enableSearchTip": "启用后可以搜索最新信息，有助于减少幻觉", "userAgent": "UA", "userAgentTip": "用户代理字符串，用于模拟浏览器访问", "theme": "主题", "systemTheme": "系统", "lightTheme": "浅色", "darkTheme": "深色", "selectTheme": "选择主题", "reset": "重置", "enableChapterValidation": "章节验证", "enableChapterValidationTip": "启用章节验证会在每章节写完后检查内容准确性，减少AI幻觉但会增加生成时间约50%-80%。重点检查引用准确性、事实性验证、内容真实性。", "enableFullTextValidation": "全文验证", "enableFullTextValidationTip": "启用全文验证会在整篇文章完成后检查全文逻辑一致性，减少跨章节矛盾但会增加总时间约30%-50%。重点检查章节间逻辑、数据冲突、整体连贯性。", "enableDualModelValidation": "双模型验证", "enableDualModelValidationTip": "启用双模型验证会使用专门的验证模型检查内容并提供修改建议，然后由写作模型应用建议。提供更专业的验证但会显著增加生成时间。", "validationModel": "验证模型", "validationModelTip": "专门用于内容验证和提供修改建议的模型，建议选择擅长分析和批判性思维的模型。", "validationNetworkingEnabled": "验证模型联网搜索", "validationNetworkingEnabledTip": "启用后，验证模型可以使用联网搜索功能来验证事实准确性、查找最新信息和核实数据。这有助于提高验证质量，但会增加处理时间和API调用成本。", "validationProvider": "验证模型提供商", "validationApiKey": "验证模型API密钥", "validationApiProxy": "验证模型API代理"}, "exportSuccess": "导出成功", "exportFailed": "导出失败"}