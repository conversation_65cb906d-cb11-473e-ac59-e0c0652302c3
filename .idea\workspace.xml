<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="3cfaeef2-f311-4a1b-85af-d4860be69bff" name="更改" comment="添加返回上一步按钮">
      <change beforePath="$PROJECT_DIR$/src/components/Research/Feedback.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/Research/Feedback.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/Research/OutlineIntegration.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/Research/OutlineIntegration.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/Research/SearchResult.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/Research/SearchResult.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/hooks/useOutlineDrivenResearch.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/hooks/useOutlineDrivenResearch.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/store/task.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/store/task.ts" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerSettings">
    <execution />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitLabMergeRequestFiltersHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPENED&quot;,
    &quot;assignee&quot;: {
      &quot;type&quot;: &quot;org.jetbrains.plugins.gitlab.mergerequest.ui.filters.GitLabMergeRequestsFiltersValue.MergeRequestsMemberFilterValue.MergeRequestsAssigneeFilterValue&quot;,
      &quot;username&quot;: &quot;zhuchangan&quot;,
      &quot;fullname&quot;: &quot;朱长安&quot;
    }
  }
}</component>
  <component name="GitLabMergeRequestsSettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;first&quot;: &quot;http://gitlab.tooking.cn/zhuchangan/deepresearch.git&quot;,
    &quot;second&quot;: &quot;e4862ecc-c2d4-446a-af15-6cbec5d8a097&quot;
  }
}</component>
  <component name="PhpWorkspaceProjectConfiguration" interpreter_name="D:\php-8.2.3\php.exe" />
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2yi2zQdF7ahy1qJlJU1bWiEsm4o" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "master",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "D:/work2/dajuassistv2",
    "node.js.detected.package.eslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "ts.external.directory.path": "D:\\work2\\deep-research-20250617\\deep-research\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-7c0b70fcd90d-JavaScript-PS-242.21829.154" />
        <option value="bundled-php-predefined-a98d8de5180a-4d9f4c849d09-com.jetbrains.php.sharedIndexes-PS-242.21829.154" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task id="LOCAL-00001" summary="修复知识库上传bug">
      <option name="closed" value="true" />
      <created>1750217653488</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1750217653488</updated>
    </task>
    <task id="LOCAL-00002" summary="添加编辑按钮">
      <option name="closed" value="true" />
      <created>1750221351901</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1750221351901</updated>
    </task>
    <task id="LOCAL-00003" summary="添加编辑按钮">
      <option name="closed" value="true" />
      <created>1750226467047</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1750226467047</updated>
    </task>
    <task active="true" id="Default" summary="默认任务">
      <changelist id="3cfaeef2-f311-4a1b-85af-d4860be69bff" name="更改" comment="添加编辑按钮" />
      <created>1750303342243</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750303342243</updated>
      <workItem from="1750303345966" duration="3873000" />
      <workItem from="1750641833488" duration="525000" />
      <workItem from="1750643196547" duration="495000" />
      <workItem from="1750644492409" duration="9000" />
      <workItem from="1750645072199" duration="9988000" />
      <workItem from="1750728143442" duration="13918000" />
      <workItem from="1750757882561" duration="1155000" />
      <workItem from="1750844015566" duration="6686000" />
      <workItem from="1750917203180" duration="757000" />
      <workItem from="1750918534959" duration="11000" />
      <workItem from="1750918767174" duration="6329000" />
      <workItem from="1751332902170" duration="7643000" />
      <workItem from="1751419469395" duration="1959000" />
      <workItem from="1751436402685" duration="6278000" />
      <workItem from="1751533066600" duration="1710000" />
      <workItem from="1751592085787" duration="15178000" />
      <workItem from="1751850645944" duration="11820000" />
      <workItem from="1751936910869" duration="5446000" />
      <workItem from="1751952652651" duration="2709000" />
      <workItem from="1751958118358" duration="2493000" />
    </task>
    <task id="LOCAL-00004" summary="编辑功能修复">
      <option name="closed" value="true" />
      <created>1750389090739</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1750389090740</updated>
    </task>
    <task id="LOCAL-00005" summary="编辑功能修复">
      <option name="closed" value="true" />
      <created>1750643322442</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1750643322443</updated>
    </task>
    <task id="LOCAL-00006" summary="编辑功能修复">
      <option name="closed" value="true" />
      <created>1750730390556</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1750730390556</updated>
    </task>
    <task id="LOCAL-00007" summary="编辑功能修复">
      <option name="closed" value="true" />
      <created>1750731035777</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1750731035777</updated>
    </task>
    <task id="LOCAL-00008" summary="编辑功能修复">
      <option name="closed" value="true" />
      <created>1750733284230</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1750733284230</updated>
    </task>
    <task id="LOCAL-00009" summary="编辑功能修复">
      <option name="closed" value="true" />
      <created>1750734913101</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1750734913101</updated>
    </task>
    <task id="LOCAL-00010" summary="编辑功能修复">
      <option name="closed" value="true" />
      <created>1750750378182</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1750750378182</updated>
    </task>
    <task id="LOCAL-00011" summary="编辑功能修复">
      <option name="closed" value="true" />
      <created>1750844051428</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1750844051428</updated>
    </task>
    <task id="LOCAL-00012" summary="编辑功能修复">
      <option name="closed" value="true" />
      <created>1750846089515</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1750846089515</updated>
    </task>
    <task id="LOCAL-00013" summary="编辑功能修复">
      <option name="closed" value="true" />
      <created>1750915341345</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1750915341345</updated>
    </task>
    <task id="LOCAL-00014" summary="编辑功能修复">
      <option name="closed" value="true" />
      <created>1750917243009</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1750917243009</updated>
    </task>
    <task id="LOCAL-00015" summary="编辑功能修复">
      <option name="closed" value="true" />
      <created>1750917961638</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1750917961638</updated>
    </task>
    <task id="LOCAL-00016" summary="文档文件去除">
      <option name="closed" value="true" />
      <created>1750919151222</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1750919151222</updated>
    </task>
    <task id="LOCAL-00017" summary="word导出">
      <option name="closed" value="true" />
      <created>1751341912429</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1751341912429</updated>
    </task>
    <task id="LOCAL-00018" summary="按钮隐藏">
      <option name="closed" value="true" />
      <created>1751346413437</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1751346413437</updated>
    </task>
    <task id="LOCAL-00019" summary="按钮隐藏">
      <option name="closed" value="true" />
      <created>1751351542469</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1751351542469</updated>
    </task>
    <task id="LOCAL-00020" summary="按钮隐藏">
      <option name="closed" value="true" />
      <created>1751354485314</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1751354485314</updated>
    </task>
    <task id="LOCAL-00021" summary="上传添加限制">
      <option name="closed" value="true" />
      <created>1751355653767</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1751355653767</updated>
    </task>
    <task id="LOCAL-00022" summary="默认设置">
      <option name="closed" value="true" />
      <created>1751356333006</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1751356333006</updated>
    </task>
    <task id="LOCAL-00023" summary="默认设置">
      <option name="closed" value="true" />
      <created>1751419621479</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1751419621479</updated>
    </task>
    <task id="LOCAL-00024" summary="上传限制">
      <option name="closed" value="true" />
      <created>1751436435757</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1751436435757</updated>
    </task>
    <task id="LOCAL-00025" summary="添加返回上一步按钮">
      <option name="closed" value="true" />
      <created>1751533537716</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1751533537716</updated>
    </task>
    <task id="LOCAL-00026" summary="添加返回上一步按钮">
      <option name="closed" value="true" />
      <created>1751953483394</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1751953483394</updated>
    </task>
    <option name="localTasksCounter" value="27" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="1111" />
    <MESSAGE value="修复知识库上传" />
    <MESSAGE value="修复知识库上传bug" />
    <MESSAGE value="添加编辑按钮" />
    <MESSAGE value="编辑功能修复" />
    <MESSAGE value="文档文件去除" />
    <MESSAGE value="word导出" />
    <MESSAGE value="按钮隐藏" />
    <MESSAGE value="上传添加限制" />
    <MESSAGE value="默认设置" />
    <MESSAGE value="上传限制" />
    <MESSAGE value="添加返回上一步按钮" />
    <option name="LAST_COMMIT_MESSAGE" value="添加返回上一步按钮" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/components/Research/Feedback.tsx</url>
          <line>183</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>