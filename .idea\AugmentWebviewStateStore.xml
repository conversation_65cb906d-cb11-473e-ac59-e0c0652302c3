<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="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" />
      </map>
    </option>
  </component>
</project>