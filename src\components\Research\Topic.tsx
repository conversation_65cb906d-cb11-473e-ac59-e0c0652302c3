"use client";
import { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import {
  SquarePlus,
  FilePlus,
  BookText,
  Paperclip,
  Building2,
} from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import ResourceList from "@/components/Knowledge/ResourceList";
import Crawler from "@/components/Knowledge/Crawler";

import BusinessAttachmentDialog from "@/components/Knowledge/BusinessAttachmentDialog";
import { Button } from "@/components/Internal/Button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import useDeepResearch from "@/hooks/useDeepResearch";
import { useSettingStore } from "@/store/setting";
import useKnowledge from "@/hooks/useKnowledge";
import useAccurateTimer from "@/hooks/useAccurateTimer";
import { useGlobalStore } from "@/store/global";
import { useTaskStore } from "@/store/task";
import { useHistoryStore } from "@/store/history";
import { FILE_UPLOAD_LIMITS } from "@/constants/limits";
import { isFileCountExceeded, isFileSizeExceeded, formatSize, calculateTotalResourceSize, countOriginalFiles } from "@/utils/file";
import { toast } from "sonner";
import OutlineCreator from "./OutlineCreator";

const formSchema = z.object({
  topic: z.string().min(1, {
    message: "研究主题不能为空",
  }),
});

function Topic() {
  const { t } = useTranslation();
  const { askQuestions } = useDeepResearch();
  const { getKnowledgeFromFile } = useKnowledge();
  const { provider } = useSettingStore();
  const { start: accurateTimerStart, stop: accurateTimerStop } =
    useAccurateTimer();
  const taskStore = useTaskStore();
  const allowResearchWithoutApiKey = false; // 临时修复：禁用无API密钥研究
  const [isThinking, setIsThinking] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [openCrawler, setOpenCrawler] = useState<boolean>(false);
  const [showOutlineCreator, setShowOutlineCreator] = useState(false);
  const [openBusinessAttachment, setOpenBusinessAttachment] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      topic: "",
    },
  });

  function handleCheck(): boolean {
    // 如果允许无API Key研究，直接通过
    if (allowResearchWithoutApiKey) {
      return true;
    }
    
    // 检查是否有provider配置
    if (!provider) {
      const { setOpenSetting } = useGlobalStore.getState();
      setOpenSetting(true);
      return false;
    }
    
    // 检查对应provider的API Key是否配置
    const store = useSettingStore.getState();
    let hasValidConfig = false;
    
    switch (provider) {
      case "google":
        hasValidConfig = !!(store.apiKey && String(store.apiKey).length > 0);
        break;
      case "openai":
        hasValidConfig = !!(store.openAIApiKey && String(store.openAIApiKey).length > 0);
        break;
      case "anthropic":
        hasValidConfig = !!(store.anthropicApiKey && String(store.anthropicApiKey).length > 0);
        break;
      case "deepseek":
        hasValidConfig = !!(store.deepseekApiKey && String(store.deepseekApiKey).length > 0);
        break;
      case "xai":
        hasValidConfig = !!(store.xAIApiKey && String(store.xAIApiKey).length > 0);
        break;
      case "mistral":
        hasValidConfig = !!(store.mistralApiKey && String(store.mistralApiKey).length > 0);
        break;
      case "azure":
        hasValidConfig = !!(store.azureApiKey && String(store.azureApiKey).length > 0);
        break;
      case "openrouter":
        hasValidConfig = !!(store.openRouterApiKey && String(store.openRouterApiKey).length > 0);
        break;
      case "openaicompatible":
        hasValidConfig = !!(store.openAICompatibleApiKey && String(store.openAICompatibleApiKey).length > 0);
        break;
      case "pollinations":
      case "ollama":
        hasValidConfig = true; // 这些provider不需要API Key
        break;
      default:
        hasValidConfig = false;
    }
    
    if (!hasValidConfig) {
      const { setOpenSetting } = useGlobalStore.getState();
      setOpenSetting(true);
      return false;
    }
    
    return true;
  }

  async function handleSubmit(values: z.infer<typeof formSchema>) {
    if (handleCheck()) {
      const { id, setQuestion } = useTaskStore.getState();
      try {
        setIsThinking(true);
        accurateTimerStart();
        if (id !== "") {
          createNewResearch();
          form.setValue("topic", values.topic);
        }
        setQuestion(values.topic);
        await askQuestions();
      } finally {
        setIsThinking(false);
        accurateTimerStop();
      }
    }
  }

  function createNewResearch() {
    const { id, backup, resetWithTopicProtection } = useTaskStore.getState();
    const { autoSave } = useHistoryStore.getState();
    
    // 检查是否有进行中的研究
    const currentTopic = taskStore.question || taskStore.title || taskStore.requirement;
    const hasProgress = taskStore.tasks.length > 0 || taskStore.finalReport || taskStore.reportPlan;
    
    if (currentTopic && hasProgress) {
      // 显示确认对话框
      const shouldContinue = confirm('当前有进行中的研究，是否保存当前进度并开始新研究？');
      if (!shouldContinue) return;
    }
    
    // 自动保存当前状态
    if (id) {
      const currentState = backup();
      autoSave(currentState);
    }
    
    resetWithTopicProtection(); // 🔥 使用保护研究主题的重置
    form.reset();
  }

  function openKnowledgeList() {
    const { setOpenKnowledge } = useGlobalStore.getState();
    setOpenKnowledge(true);
  }

  async function handleFileUpload(files: FileList | null) {
    if (files) {
      const fileArray = Array.from(files);
      
      // 检查文件数量限制（使用原始文件数量）
      if (isFileCountExceeded(countOriginalFiles(taskStore.resources), fileArray.length, FILE_UPLOAD_LIMITS.MAX_FILE_COUNT)) {
        toast.error(`文件数量超过限制，最多只能添加 ${FILE_UPLOAD_LIMITS.MAX_FILE_COUNT} 个原始文件`);
        return;
      }
      
      // 检查每个文件的大小限制
      for (const file of fileArray) {
        if (isFileSizeExceeded(file, FILE_UPLOAD_LIMITS.MAX_FILE_SIZE)) {
          toast.error(`文件 "${file.name}" 大小超过限制，单个文件最大为 ${formatSize(FILE_UPLOAD_LIMITS.MAX_FILE_SIZE)}`);
          return;
        }
      }
      
      // 所有验证通过，开始上传文件
      for await (const file of fileArray) {
        await getKnowledgeFromFile(file);
      }
      
      // Clear the input file to avoid processing the previous file multiple times
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  }

  // 处理业务附件选择
  function handleBusinessAttachmentConfirm(attachments: any[]) {
    const { addResource } = taskStore;
    
    attachments.forEach(attachment => {
      // 转换为资源格式
      const convertFileSize = (size: string): number => {
        const num = parseFloat(size);
        if (size.includes('MB')) {
          return num * 1024 * 1024;
        } else if (size.includes('KB')) {
          return num * 1024;
        }
        return num;
      };

      addResource({
        id: attachment.id,
        name: attachment.filename,
        type: "business_attachment",
        size: convertFileSize(attachment.size),
        status: "completed",
      });
    });
  }

  // 🔥 新增：监听表单字段变化
  const watchedTopic = form.watch("topic");

  useEffect(() => {
    const currentTopic = taskStore.question || taskStore.requirement || taskStore.title || "";
    
    if (currentTopic && currentTopic !== form.getValues("topic")) {
      form.setValue("topic", currentTopic);
    }
  }, [taskStore.question, taskStore.requirement, taskStore.title, form]);

  useEffect(() => {
    // 延迟检查，确保zustand persist完成数据恢复
    const timer = setTimeout(() => {
      const currentTopic = taskStore.question || taskStore.requirement || taskStore.title || "";
      if (currentTopic && !form.getValues("topic")) {
        form.setValue("topic", currentTopic);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  // 🔥 新增：实时同步表单值到 taskStore（包括粘贴操作）
  useEffect(() => {
    if (watchedTopic && watchedTopic.trim() !== '') {
      // 使用防抖，避免频繁更新
      const debounceTimer = setTimeout(() => {
        const { setQuestion, setRequirement } = useTaskStore.getState();
        setQuestion(watchedTopic.trim());
        
        // 🔥 如果 requirement 为空，也同步设置为 topic 内容
        const currentRequirement = useTaskStore.getState().requirement;
        if (!currentRequirement || currentRequirement.trim() === '') {
          setRequirement(`研究主题：${watchedTopic.trim()}`);
          console.log('🔄 同时同步 requirement 到 taskStore');
        }
        
        console.log('🔄 实时同步研究主题到 taskStore:', watchedTopic.trim());
      }, 300); // 300ms 防抖

      return () => clearTimeout(debounceTimer);
    }
  }, [watchedTopic]);

  return (
    <section className="p-4 border rounded-md mt-4 print:hidden">
      <div className="flex justify-between items-center border-b mb-2">
        <h3 className="font-semibold text-lg leading-10">
          {t("research.topic.title")}
        </h3>
        <div className="flex gap-1">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => createNewResearch()}
            title={t("research.common.newResearch")}
          >
            <SquarePlus />
          </Button>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)}>
          <FormField
            control={form.control}
            name="topic"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="mb-2 text-base font-semibold">
                  {t("research.topic.topicLabel")}
                </FormLabel>
                <FormControl>
                  <Textarea
                    rows={3}
                    placeholder={t("research.topic.topicPlaceholder")}
                    {...field}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormItem className="mt-2">
            <FormLabel className="mb-2 text-base font-semibold">
              {t("knowledge.localResourceTitle")}
            </FormLabel>
            <FormControl onSubmit={(ev) => ev.stopPropagation()}>
              <div>
                {taskStore.resources.length > 0 ? (
                  <ResourceList
                    className="pb-2 mb-2 border-b"
                    resources={taskStore.resources}
                    onRemove={taskStore.removeResource}
                  />
                ) : null}
                <div className="flex gap-2">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <div className="inline-flex border p-2 rounded-md text-sm cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800">
                        <FilePlus className="w-5 h-5" />
                        <span className="ml-1">{t("knowledge.addResource")}</span>
                        {taskStore.resources.length > 0 && (
                          <span className="ml-2 text-xs text-gray-500">
                            ({countOriginalFiles(taskStore.resources)}/{FILE_UPLOAD_LIMITS.MAX_FILE_COUNT} 个, {formatSize(calculateTotalResourceSize(taskStore.resources))})
                          </span>
                        )}
                      </div>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => openKnowledgeList()}>
                        <BookText />
                        <span>{t("knowledge.knowledge")}</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() =>
                          handleCheck() && fileInputRef.current?.click()
                        }
                      >
                        <Paperclip />
                        <span>{t("knowledge.localFile")}</span>
                      </DropdownMenuItem>

                    </DropdownMenuContent>
                  </DropdownMenu>
                  
                  <div 
                    className="inline-flex border p-2 rounded-md text-sm cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800"
                    onClick={() => handleCheck() && setOpenBusinessAttachment(true)}
                  >
                    <Building2 className="w-5 h-5" />
                    <span className="ml-1">添加业务附件</span>
                  </div>
                </div>
              </div>
            </FormControl>
          </FormItem>
        </form>
      </Form>
      <aside className="print:hidden">
        <Crawler
          open={openCrawler}
          onClose={() => setOpenCrawler(false)}
        ></Crawler>
        <input
          type="file"
          className="hidden"
          ref={fileInputRef}
          onChange={(ev) => handleFileUpload(ev.target.files)}
          multiple
        ></input>
        <OutlineCreator 
          open={showOutlineCreator}
          onClose={() => setShowOutlineCreator(false)}
        />
        <BusinessAttachmentDialog
          open={openBusinessAttachment}
          onClose={() => setOpenBusinessAttachment(false)}
          onConfirm={handleBusinessAttachmentConfirm}
        />
      </aside>
    </section>
  );
}

export default Topic;
