"use client";
import dynamic from "next/dynamic";
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderCircle } from "lucide-react";
import { Button } from "@/components/Internal/Button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import useDeepResearch from "@/hooks/useDeepResearch";
import { useOutlineDrivenResearch } from "@/hooks/useOutlineDrivenResearch";
import useAccurateTimer from "@/hooks/useAccurateTimer";
import { useTaskStore } from "@/store/task";
import { useGlobalStore } from "@/store/global";
import OutlineIntegration from "./OutlineIntegration";
// import ChapterResearchProgress from "./ChapterResearchProgress";

const MagicDown = dynamic(() => import("@/components/MagicDown"));

const formSchema = z.object({
  feedback: z.string(),
});

function Feedback() {
  const { t } = useTranslation();
  const taskStore = useTaskStore();
  const { status, deepResearch, writeReportPlan, askQuestions } = useDeepResearch();
  const {
    startOutlineDrivenResearch,
    isResearching: isOutlineResearching,
    isPaused: isOutlinePaused,
    pauseResearch,
    resumeResearch,
    resetResearch,
    forceStopResearch
    // researchChapter,
    // writeChapterContent
  } = useOutlineDrivenResearch();
  const {
    formattedTime,
    start: accurateTimerStart,
    stop: accurateTimerStop,
  } = useAccurateTimer();
  const [isThinking, setIsThinking] = useState<boolean>(false);
  const [isResearch, setIsResaerch] = useState<boolean>(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      feedback: taskStore.feedback,
    },
  });

  async function startDeepResearch() {
    try {
      accurateTimerStart();
      setIsResaerch(true);
      await deepResearch();
    } finally {
      setIsResaerch(false);
      accurateTimerStop();
    }
  }

  // 传统思考流程
  async function handleTraditionalThinking() {
    try {
      setIsThinking(true);
      accurateTimerStart();
      await askQuestions();
    } finally {
      setIsThinking(false);
      accurateTimerStop();
    }
  }

  // 大纲驱动流程 - 打开大纲管理器
  function handleOutlineDriven() {
    const { setOpenOutlineManager } = useGlobalStore.getState();
    setOpenOutlineManager(true);
  }

  async function handleSubmit(values: z.infer<typeof formSchema>) {
    const { question, questions, setFeedback } = useTaskStore.getState();
    setFeedback(values.feedback);
    const prompt = [
      `Initial Query: ${question}`,
      `Follow-up Questions: ${questions}`,
      `Follow-up Feedback: ${values.feedback}`,
    ].join("\n\n");
    taskStore.setQuery(prompt);
    try {
      accurateTimerStart();
      setIsThinking(true);
      await writeReportPlan();
      setIsThinking(false);
    } finally {
      accurateTimerStop();
    }
  }

  useEffect(() => {
    form.setValue("feedback", taskStore.feedback);
  }, [taskStore.feedback, form]);

  // 🔥 新增：检测页面刷新后的恢复状态
  const hasIncompleteResearch = taskStore.isOutlineDriven &&
    taskStore.researchOutline &&
    taskStore.writingProgress &&
    taskStore.writingProgress.totalProgress < 100 &&
    taskStore.writingProgress.totalProgress > 0;

  return (
    <section className="p-4 border rounded-md mt-4 print:hidden">
      <h3 className="font-semibold text-lg border-b mb-2 leading-10">
        {t("research.feedback.title")}
      </h3>
      {/* 如果处于异常状态，显示重置按钮 */}
      {taskStore.isOutlineDriven && !taskStore.researchOutline && taskStore.questions === "" && (
        <div className="mb-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
          <div className="text-sm text-orange-800 mb-2">
            检测到研究状态异常，请重置后重新开始：
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              taskStore.setOutlineDriven(false);
              taskStore.setResearchOutline(null);
            }}
          >
            重置研究状态
          </Button>
        </div>
      )}
      {taskStore.questions === "" && taskStore.reportPlan === "" && !taskStore.researchOutline ? (
        // 显示两个路径选择按钮（无论question是否为空）
        <div className="space-y-4">
          <div className="text-center text-gray-600 mb-4">
            请选择研究方式：
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 传统思考流程 */}
            <div className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 transition-colors">
              <div className="text-center">
                <h4 className="font-semibold text-lg mb-2">💭 开始思考</h4>
                <p className="text-sm text-gray-600 mb-4">
                  AI将分析您的问题，生成相关思考问题和研究计划
                </p>
                <Button
                  className="w-full"
                  onClick={handleTraditionalThinking}
                  disabled={isThinking}
                >
                  {isThinking ? (
                    <>
                      <LoaderCircle className="animate-spin" />
                      <span>思考中...</span>
                      <small className="font-mono">{formattedTime}</small>
                    </>
                  ) : (
                    "开始思考"
                  )}
                </Button>
              </div>
            </div>
            
            {/* 大纲驱动流程 */}
            <div className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-green-400 transition-colors">
              <div className="text-center">
                <h4 className="font-semibold text-lg mb-2">📋 大纲开始研究(可选)</h4>
                <p className="text-sm text-gray-600 mb-4">
                  选择或创建结构化大纲，进行系统性深度研究
                </p>
                <Button
                  className="w-full"
                  variant="outline"
                  onClick={handleOutlineDriven}
                  disabled={isThinking}
                >
                  选择大纲模板
                </Button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div>
          {/* 传统研究-系统提问 */}
          {taskStore.questions !== "" && (
            <>
              <h4 className="mt-4 text-base font-semibold">
                {t("research.feedback.questions")}
              </h4>
              <MagicDown
                className="mt-2 min-h-20"
                value={taskStore.questions}
                onChange={(value) => taskStore.updateQuestions(value)}
              ></MagicDown>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(handleSubmit)}>
                  <FormField
                    control={form.control}
                    name="feedback"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="mb-2 font-semibold">
                          {t("research.feedback.feedbackLabel")}
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            rows={3}
                            placeholder={t("research.feedback.feedbackPlaceholder")}
                            disabled={isThinking}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <Button
                    className="mt-4 w-full"
                    type="submit"
                    disabled={isThinking}
                  >
                    {isThinking ? (
                      <>
                        <LoaderCircle className="animate-spin" />
                        <span>{status}</span>
                        <small className="font-mono">{formattedTime}</small>
                      </>
                    ) : taskStore.reportPlan === "" ? (
                      t("research.common.writeReportPlan")
                    ) : (
                      t("research.common.rewriteReportPlan")
                    )}
                  </Button>
                </form>
              </Form>
            </>
          )}

          {/* 🔥 大纲概览显示 */}
          {taskStore.isOutlineDriven && taskStore.researchOutline && (
            <>
              <h4 className="mt-4 text-base font-semibold">
                大纲概览
              </h4>
              <div className="mt-2 p-4 border rounded-md bg-blue-50 dark:bg-blue-950">
                <h5 className="font-medium mb-2">{taskStore.researchOutline.title}</h5>
                <p className="text-sm text-muted-foreground mb-3">
                  {taskStore.researchOutline.description}
                </p>
                <div className="space-y-1 mb-4">
                  <p className="text-sm font-medium">章节概览：</p>
                  {taskStore.researchOutline.chapters
                    .filter((c: any) => c.enabled)
                    .map((chapter: any, i: number) => (
                      <div key={chapter.id} className="text-sm text-muted-foreground">
                        第{i + 1}章：{chapter.title}
                      </div>
                    ))}
                </div>
                {taskStore.tasks.length === 0 && taskStore.chapterResearchProgress === "" && !hasIncompleteResearch ? (
                  <div className="space-y-3">
                    <p className="text-sm text-muted-foreground">
                      大纲已确定，点击下方按钮开始分章节研究。
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <Button
                        variant="outline"
                        onClick={() => {
                          // 返回重新选择：清除大纲相关状态
                          taskStore.setOutlineDriven(false);
                          taskStore.setResearchOutline(null);
                          taskStore.setChapterResearchProgress("");
                        }}
                        disabled={isThinking || isOutlineResearching}
                      >
                        ← 返回重新选择
                      </Button>
                      <Button
                        onClick={() => startOutlineDrivenResearch(taskStore.researchOutline!)}
                        disabled={isThinking || isOutlineResearching}
                      >
                        {(isThinking || isOutlineResearching) ? (
                          <>
                            <LoaderCircle className="animate-spin" />
                            <span>研究中...</span>
                          </>
                        ) : (
                          "开始分章节研究"
                        )}
                      </Button>
                    </div>
                  </div>
                ) : hasIncompleteResearch ? (
                  <div className="space-y-3">
                    <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
                      <div className="text-sm text-orange-800 mb-2">
                        检测到未完成的研究进度 ({taskStore.writingProgress?.totalProgress || 0}%)，您可以选择：
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                        <Button
                          size="sm"
                          onClick={() => startOutlineDrivenResearch(taskStore.researchOutline!)}
                          disabled={isThinking || isOutlineResearching}
                        >
                          {(isThinking || isOutlineResearching) ? (
                            <>
                              <LoaderCircle className="animate-spin" />
                              <span>继续中...</span>
                            </>
                          ) : (
                            "继续研究"
                          )}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            // 重新开始：重置进度但保留大纲
                            taskStore.resetWritingProgress();
                            taskStore.setChapterResearchProgress("");
                            taskStore.update([]); // 清空任务
                            startOutlineDrivenResearch(taskStore.researchOutline!);
                          }}
                          disabled={isThinking || isOutlineResearching}
                        >
                          重新开始
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            taskStore.setOutlineDriven(false);
                            taskStore.setResearchOutline(null);
                            taskStore.setChapterResearchProgress("");
                            taskStore.resetWritingProgress();
                          }}
                          disabled={isThinking || isOutlineResearching}
                        >
                          返回选择
                        </Button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {isOutlinePaused ? (
                      <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div className="text-sm text-yellow-800 mb-2">
                          研究已暂停，您可以继续研究或重置状态。
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                          <Button
                            size="sm"
                            onClick={resumeResearch}
                            disabled={isThinking || (isOutlineResearching && !isOutlinePaused)}
                          >
                            {(isThinking || (isOutlineResearching && !isOutlinePaused)) ? (
                              <>
                                <LoaderCircle className="animate-spin" />
                                <span>恢复中...</span>
                              </>
                            ) : (
                              "继续研究"
                            )}
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={resetResearch}
                            disabled={isThinking || isOutlineResearching}
                          >
                            重置状态
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              taskStore.setOutlineDriven(false);
                              taskStore.setResearchOutline(null);
                              taskStore.setChapterResearchProgress("");
                              resetResearch();
                            }}
                            disabled={isThinking || isOutlineResearching}
                          >
                            返回选择
                          </Button>
                        </div>
                      </div>
                    ) : isOutlineResearching ? (
                      <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="text-sm text-blue-800 mb-2">
                          分章节研究进行中，您可以暂停研究。
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => pauseResearch("用户手动暂停")}
                            disabled={isThinking}
                          >
                            暂停研究
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={forceStopResearch}
                            disabled={isThinking}
                          >
                            强制停止
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">
                        分章节研究已完成，请在预写区域查看结果。
                      </p>
                    )}
                  </div>
                )}
              </div>
            </>
          )}

        </div>
      )}
      {taskStore.reportPlan !== "" && !taskStore.isOutlineDriven ? (
        <div className="mt-6">
          <h4 className="text-base font-semibold">
            {t("research.feedback.reportPlan")}
          </h4>
          <MagicDown
            className="mt-2 min-h-20"
            value={taskStore.reportPlan}
            onChange={(value) => taskStore.updateReportPlan(value)}
          ></MagicDown>
          <Button
            className="w-full mt-4"
            variant="default"
            onClick={() => startDeepResearch()}
            disabled={isResearch}
          >
            {isResearch ? (
              <>
                <LoaderCircle className="animate-spin" />
                <span>{status}</span>
                <small className="font-mono">{formattedTime}</small>
              </>
            ) : taskStore.tasks.length === 0 ? (
              t("research.common.startResearch")
            ) : (
              t("research.common.restartResearch")
            )}
          </Button>
        </div>
      ) : null}
      
      {/* 大纲整合功能 - 只在传统研究模式下显示 */}
      {taskStore.reportPlan !== "" && !taskStore.isOutlineDriven && (
        <div className="mt-6">
          <OutlineIntegration 
            onStartOutlineDrivenResearch={startOutlineDrivenResearch}
          />
        </div>
      )}
      
      {/* 章节研究进度 - 暂时注释，待后续完善 */}
      {/* <ChapterResearchProgress /> */}
    </section>
  );
}

export default Feedback;
